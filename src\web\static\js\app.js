/**
 * Maltego Analyzer - Main JavaScript Application
 */

// Application state
const MaltegoApp = {
    currentAnalysis: null,
    isAnalyzing: false,
    
    // Initialize the application
    init() {
        this.setupEventListeners();
        this.loadSavedSettings();
        console.log('Maltego Analyzer initialized');
    },
    
    // Setup event listeners
    setupEventListeners() {
        // Form validation
        const form = document.getElementById('analysisForm');
        if (form) {
            form.addEventListener('input', this.validateForm.bind(this));
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
        
        // Window resize handler for graph
        window.addEventListener('resize', this.handleResize.bind(this));
        
        // Auto-save settings
        const inputs = ['entityType', 'maxDepth'];
        inputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', this.saveSettings.bind(this));
            }
        });
    },
    
    // Validate form inputs
    validateForm() {
        const inputValue = document.getElementById('inputValue');
        const submitBtn = document.querySelector('#analysisForm button[type="submit"]');
        
        if (!inputValue || !submitBtn) return;
        
        const isValid = inputValue.value.trim().length > 0;
        submitBtn.disabled = !isValid || this.isAnalyzing;
        
        // Update input styling
        if (inputValue.value.trim().length > 0) {
            inputValue.classList.remove('is-invalid');
            inputValue.classList.add('is-valid');
        } else {
            inputValue.classList.remove('is-valid');
            if (inputValue.value.length > 0) {
                inputValue.classList.add('is-invalid');
            }
        }
    },
    
    // Handle keyboard shortcuts
    handleKeyboardShortcuts(event) {
        // Ctrl+Enter to start analysis
        if (event.ctrlKey && event.key === 'Enter') {
            event.preventDefault();
            const form = document.getElementById('analysisForm');
            if (form && !this.isAnalyzing) {
                form.dispatchEvent(new Event('submit'));
            }
        }
        
        // Escape to close modals
        if (event.key === 'Escape') {
            const modals = document.querySelectorAll('.modal.show');
            modals.forEach(modal => {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) bsModal.hide();
            });
        }
    },
    
    // Handle window resize
    handleResize() {
        // Resize Plotly graph if it exists
        const graphContainer = document.getElementById('graphContainer');
        if (graphContainer && window.Plotly) {
            const plotlyDiv = graphContainer.querySelector('.plotly-graph-div');
            if (plotlyDiv) {
                window.Plotly.Plots.resize(plotlyDiv);
            }
        }
    },
    
    // Save user settings to localStorage
    saveSettings() {
        const settings = {
            entityType: document.getElementById('entityType')?.value || '',
            maxDepth: document.getElementById('maxDepth')?.value || '2',
            timestamp: Date.now()
        };
        
        try {
            localStorage.setItem('maltegoSettings', JSON.stringify(settings));
        } catch (e) {
            console.warn('Failed to save settings:', e);
        }
    },
    
    // Load saved settings from localStorage
    loadSavedSettings() {
        try {
            const saved = localStorage.getItem('maltegoSettings');
            if (saved) {
                const settings = JSON.parse(saved);
                
                // Only load if saved within last 7 days
                if (Date.now() - settings.timestamp < 7 * 24 * 60 * 60 * 1000) {
                    const entityType = document.getElementById('entityType');
                    const maxDepth = document.getElementById('maxDepth');
                    
                    if (entityType && settings.entityType) {
                        entityType.value = settings.entityType;
                    }
                    if (maxDepth && settings.maxDepth) {
                        maxDepth.value = settings.maxDepth;
                    }
                }
            }
        } catch (e) {
            console.warn('Failed to load settings:', e);
        }
    },
    
    // Enhanced alert system
    showAlert(message, type = 'info', duration = 5000) {
        const alertContainer = document.getElementById('alertContainer');
        if (!alertContainer) return;
        
        const alertId = 'alert_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        const icons = {
            success: 'fas fa-check-circle',
            danger: 'fas fa-exclamation-triangle',
            warning: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle'
        };
        
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show shadow-sm" id="${alertId}" role="alert">
                <i class="${icons[type] || icons.info} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        alertContainer.insertAdjacentHTML('beforeend', alertHtml);
        
        // Auto-remove after specified duration
        if (duration > 0) {
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    const bsAlert = bootstrap.Alert.getInstance(alert);
                    if (bsAlert) {
                        bsAlert.close();
                    } else {
                        alert.remove();
                    }
                }
            }, duration);
        }
        
        return alertId;
    },
    
    // Copy text to clipboard
    copyToClipboard(text, successMessage = 'Copied to clipboard') {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showAlert(successMessage, 'success', 2000);
            }).catch(err => {
                console.error('Failed to copy:', err);
                this.showAlert('Failed to copy to clipboard', 'danger');
            });
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                this.showAlert(successMessage, 'success', 2000);
            } catch (err) {
                console.error('Failed to copy:', err);
                this.showAlert('Failed to copy to clipboard', 'danger');
            }
            document.body.removeChild(textArea);
        }
    },
    
    // Format numbers with commas
    formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    
    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // Debounce function for performance
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Enhanced error handling
    handleError(error, context = 'Unknown') {
        console.error(`Error in ${context}:`, error);
        
        let message = 'An unexpected error occurred';
        if (error.message) {
            message = error.message;
        } else if (typeof error === 'string') {
            message = error;
        }
        
        this.showAlert(`${context}: ${message}`, 'danger');
    },
    
    // Network status monitoring
    monitorNetworkStatus() {
        const updateOnlineStatus = () => {
            if (!navigator.onLine) {
                this.showAlert('You are currently offline. Some features may not work.', 'warning', 0);
            }
        };
        
        window.addEventListener('online', () => {
            this.showAlert('Connection restored', 'success', 3000);
        });
        
        window.addEventListener('offline', updateOnlineStatus);
        
        // Check initial status
        updateOnlineStatus();
    },
    
    // Performance monitoring
    measurePerformance(name, fn) {
        const start = performance.now();
        const result = fn();
        const end = performance.now();
        console.log(`${name} took ${(end - start).toFixed(2)} milliseconds`);
        return result;
    }
};

// Utility functions
const Utils = {
    // Validate email address
    isValidEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },
    
    // Validate IP address
    isValidIP(ip) {
        const ipv4 = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const ipv6 = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
        return ipv4.test(ip) || ipv6.test(ip);
    },
    
    // Validate domain
    isValidDomain(domain) {
        const re = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
        return re.test(domain);
    },
    
    // Auto-detect entity type
    detectEntityType(value) {
        value = value.trim();
        
        if (this.isValidEmail(value)) return 'email';
        if (this.isValidIP(value)) return 'ip_address';
        if (this.isValidDomain(value)) return 'domain';
        if (/^\+?[\d\s\-\(\)]+$/.test(value) && value.replace(/\D/g, '').length >= 10) return 'phone_number';
        
        return 'unknown';
    }
};

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    MaltegoApp.init();
    MaltegoApp.monitorNetworkStatus();
});

// Export for global access
window.MaltegoApp = MaltegoApp;
window.Utils = Utils;
