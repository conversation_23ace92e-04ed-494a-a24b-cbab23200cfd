"""
IP address related transforms
"""

import requests
from typing import List, Dict, Any
from ..entities.base import Entity, EntityType
from ..datasources.base import DataSourceManager
from .base import BaseTransform


class IPToGeolocationTransform(BaseTransform):
    """Transform to get geolocation information for an IP address"""
    
    @property
    def name(self) -> str:
        return "ip_to_geolocation"
    
    @property
    def description(self) -> str:
        return "Get geographic location information for an IP address"
    
    @property
    def input_types(self) -> List[str]:
        return [EntityType.IP_ADDRESS.value]
    
    @property
    def output_types(self) -> List[str]:
        return [
            EntityType.LOCATION.value,
            EntityType.ORGANIZATION.value
        ]
    
    def execute(self, entity: Entity) -> List[Entity]:
        """Get geolocation for IP address"""
        if not self.validate_input(entity):
            return []
        
        entities = []
        
        try:
            # Use free IP geolocation service (ip-api.com)
            response = requests.get(
                f"http://ip-api.com/json/{entity.value}",
                timeout=10
            )
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') == 'success':
                # Create location entity
                location_parts = []
                if data.get('city'):
                    location_parts.append(data['city'])
                if data.get('regionName'):
                    location_parts.append(data['regionName'])
                if data.get('country'):
                    location_parts.append(data['country'])
                
                if location_parts:
                    location_value = ', '.join(location_parts)
                    location_entity = Entity(
                        type=EntityType.LOCATION.value,
                        value=location_value,
                        confidence=0.8,
                        metadata={
                            'source': 'ip_geolocation',
                            'latitude': data.get('lat'),
                            'longitude': data.get('lon'),
                            'city': data.get('city'),
                            'region': data.get('regionName'),
                            'country': data.get('country'),
                            'country_code': data.get('countryCode'),
                            'timezone': data.get('timezone'),
                            'zip_code': data.get('zip'),
                            'original_ip': entity.value
                        },
                        source=self.name
                    )
                    entities.append(location_entity)
                
                # Create ISP/organization entity
                if data.get('isp'):
                    isp_entity = Entity(
                        type=EntityType.ORGANIZATION.value,
                        value=data['isp'],
                        confidence=0.7,
                        metadata={
                            'source': 'ip_geolocation',
                            'role': 'isp',
                            'org': data.get('org'),
                            'as': data.get('as'),
                            'original_ip': entity.value
                        },
                        source=self.name
                    )
                    entities.append(isp_entity)
                
                self.logger.info(f"Geolocation found for {entity.value}: {location_value}")
            else:
                self.logger.warning(f"Geolocation lookup failed for {entity.value}: {data.get('message')}")
                
        except Exception as e:
            self.logger.error(f"Error in geolocation lookup for {entity.value}: {e}")
        
        return self.filter_results(entities)


class IPToDNSTransform(BaseTransform):
    """Transform to get reverse DNS information for an IP address"""
    
    @property
    def name(self) -> str:
        return "ip_to_dns"
    
    @property
    def description(self) -> str:
        return "Get reverse DNS information for an IP address"
    
    @property
    def input_types(self) -> List[str]:
        return [EntityType.IP_ADDRESS.value]
    
    @property
    def output_types(self) -> List[str]:
        return [EntityType.DOMAIN.value]
    
    def execute(self, entity: Entity) -> List[Entity]:
        """Execute reverse DNS lookup for IP"""
        if not self.validate_input(entity):
            return []
        
        # Get DNS data sources
        dns_sources = [
            source for source in self.datasource_manager.sources.values()
            if source.name == 'dns' and source.supports_entity_type(entity.type)
        ]
        
        all_entities = []
        
        for source in dns_sources:
            try:
                result = source.query(entity)
                if result.success:
                    all_entities.extend(result.entities)
                    self.logger.info(f"Reverse DNS lookup found {len(result.entities)} entities")
                else:
                    self.logger.warning(f"Reverse DNS lookup failed: {result.error_message}")
                    
            except Exception as e:
                self.logger.error(f"Error in reverse DNS lookup: {e}")
        
        return self.filter_results(all_entities)


class IPToPortScanTransform(BaseTransform):
    """Transform to scan common ports on an IP address"""
    
    @property
    def name(self) -> str:
        return "ip_to_ports"
    
    @property
    def description(self) -> str:
        return "Scan common ports on an IP address to identify running services"
    
    @property
    def input_types(self) -> List[str]:
        return [EntityType.IP_ADDRESS.value]
    
    @property
    def output_types(self) -> List[str]:
        return [EntityType.URL.value]
    
    def execute(self, entity: Entity) -> List[Entity]:
        """Scan common ports on IP address"""
        if not self.validate_input(entity):
            return []
        
        import socket
        from contextlib import closing
        
        # Common ports to scan
        common_ports = {
            21: 'ftp',
            22: 'ssh',
            23: 'telnet',
            25: 'smtp',
            53: 'dns',
            80: 'http',
            110: 'pop3',
            143: 'imap',
            443: 'https',
            993: 'imaps',
            995: 'pop3s',
            3389: 'rdp',
            5432: 'postgresql',
            3306: 'mysql',
            1433: 'mssql',
            6379: 'redis',
            27017: 'mongodb'
        }
        
        entities = []
        
        for port, service in common_ports.items():
            try:
                with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as sock:
                    sock.settimeout(2)  # 2 second timeout
                    result = sock.connect_ex((entity.value, port))
                    
                    if result == 0:  # Port is open
                        # Create URL entity for HTTP/HTTPS services
                        if service in ['http', 'https']:
                            url_value = f"{service}://{entity.value}:{port}"
                            url_entity = Entity(
                                type=EntityType.URL.value,
                                value=url_value,
                                confidence=0.9,
                                metadata={
                                    'source': 'port_scan',
                                    'port': port,
                                    'service': service,
                                    'original_ip': entity.value
                                },
                                source=self.name
                            )
                            entities.append(url_entity)
                        
                        self.logger.info(f"Found open port {port} ({service}) on {entity.value}")
                        
            except Exception as e:
                self.logger.debug(f"Error scanning port {port} on {entity.value}: {e}")
                continue
        
        return self.filter_results(entities)
