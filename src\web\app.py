"""
Flask web application for Maltego
"""

import os
import json
import logging
from flask import Flask, render_template, request, jsonify, send_file
from datetime import datetime
import tempfile

# Import Maltego components
from ..core.config import Config
from ..core.engine import TransformEngine
from ..entities.base import EntityRecognizer
from ..datasources.base import DataSourceManager
from ..datasources.whois_source import WhoisDataSource
from ..datasources.dns_source import DNSDataSource
from ..transforms.base import TransformRegistry
from ..transforms.domain_transforms import (
    DomainToWhoisTransform, DomainToDNSTransform, 
    EmailToDomainTransform, DomainToSubdomainTransform
)
from ..transforms.ip_transforms import (
    IPToGeolocationTransform, IPToDNSTransform, IPToPortScanTransform
)
from ..visualization.graph import GraphVisualizer


def create_app(config_path: str = "config/config.yaml") -> Flask:
    """Create and configure the Flask application"""
    app = Flask(__name__)
    
    # Load configuration
    config = Config.load_from_file(config_path)
    config.ensure_directories()
    
    # Configure Flask
    app.config['SECRET_KEY'] = config.security.session_secret_key
    app.config['DEBUG'] = config.app.debug
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, config.logging.level),
        format=config.logging.format,
        handlers=[
            logging.FileHandler(config.logging.file),
            logging.StreamHandler()
        ]
    )
    
    # Initialize components
    datasource_manager = DataSourceManager(config.datasources)
    transform_registry = TransformRegistry()
    engine = TransformEngine(config)
    visualizer = GraphVisualizer(config.visualization.__dict__)
    
    # Register data sources
    if config.is_datasource_enabled('whois'):
        whois_source = WhoisDataSource(config.datasources.get('whois', {}))
        datasource_manager.register_source(whois_source)
    
    if config.is_datasource_enabled('dns'):
        dns_source = DNSDataSource(config.datasources.get('dns', {}))
        datasource_manager.register_source(dns_source)
    
    # Register transforms
    transforms = [
        DomainToWhoisTransform(config.transforms.__dict__, datasource_manager),
        DomainToDNSTransform(config.transforms.__dict__, datasource_manager),
        EmailToDomainTransform(config.transforms.__dict__, datasource_manager),
        DomainToSubdomainTransform(config.transforms.__dict__, datasource_manager),
        IPToGeolocationTransform(config.transforms.__dict__, datasource_manager),
        IPToDNSTransform(config.transforms.__dict__, datasource_manager),
        IPToPortScanTransform(config.transforms.__dict__, datasource_manager)
    ]
    
    for transform in transforms:
        transform_registry.register(transform)
        engine.register_transform(transform)
    
    @app.route('/')
    def index():
        """Main page"""
        return render_template('index.html', 
                             available_transforms=transform_registry.get_transform_names())
    
    @app.route('/api/analyze', methods=['POST'])
    def analyze():
        """Analyze an entity"""
        try:
            data = request.get_json()
            
            if not data or 'input' not in data:
                return jsonify({'error': 'Missing input data'}), 400
            
            input_value = data['input'].strip()
            entity_type = data.get('type')
            transform_names = data.get('transforms', [])
            max_depth = data.get('max_depth', 2)
            
            if not input_value:
                return jsonify({'error': 'Input value cannot be empty'}), 400
            
            # Create entity
            entity = EntityRecognizer.create_entity(input_value, entity_type)
            
            # Run analysis
            results = engine.analyze_entity(
                entity=entity,
                transform_names=transform_names if transform_names else None,
                max_depth=max_depth
            )
            
            # Create visualization data
            nodes, edges = visualizer.create_graph_from_analysis(results)
            graph_stats = visualizer.get_graph_statistics(nodes, edges)
            
            # Convert to JSON-serializable format
            response_data = {
                'success': True,
                'input_entity': entity.to_dict(),
                'metadata': results['metadata'],
                'statistics': graph_stats,
                'entities': {k: v.to_dict() for k, v in results['entities'].items()},
                'connections': results['connections'],
                'graph_data': {
                    'nodes': [
                        {
                            'id': node.id,
                            'label': node.label,
                            'type': node.type,
                            'size': node.size,
                            'color': node.color,
                            'metadata': node.metadata
                        }
                        for node in nodes
                    ],
                    'edges': [
                        {
                            'source': edge.source,
                            'target': edge.target,
                            'label': edge.label,
                            'weight': edge.weight,
                            'color': edge.color,
                            'metadata': edge.metadata
                        }
                        for edge in edges
                    ]
                }
            }
            
            return jsonify(response_data)
            
        except Exception as e:
            logging.error(f"Analysis failed: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/visualize', methods=['POST'])
    def visualize():
        """Create visualization from analysis results"""
        try:
            data = request.get_json()
            
            if not data or 'graph_data' not in data:
                return jsonify({'error': 'Missing graph data'}), 400
            
            graph_data = data['graph_data']
            
            # Recreate nodes and edges
            nodes = [
                GraphNode(
                    id=node['id'],
                    label=node['label'],
                    type=node['type'],
                    size=node['size'],
                    color=node['color'],
                    metadata=node['metadata']
                )
                for node in graph_data['nodes']
            ]
            
            edges = [
                GraphEdge(
                    source=edge['source'],
                    target=edge['target'],
                    label=edge['label'],
                    weight=edge['weight'],
                    color=edge['color'],
                    metadata=edge['metadata']
                )
                for edge in graph_data['edges']
            ]
            
            # Create Plotly figure
            fig = visualizer.create_plotly_figure(nodes, edges)
            
            return jsonify({
                'success': True,
                'plot_json': fig.to_json()
            })
            
        except Exception as e:
            logging.error(f"Visualization failed: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/export/<format>', methods=['POST'])
    def export_data(format):
        """Export analysis results in various formats"""
        try:
            data = request.get_json()
            
            if not data or 'graph_data' not in data:
                return jsonify({'error': 'Missing graph data'}), 400
            
            graph_data = data['graph_data']
            
            # Recreate nodes and edges
            nodes = [
                GraphNode(
                    id=node['id'],
                    label=node['label'],
                    type=node['type'],
                    size=node['size'],
                    color=node['color'],
                    metadata=node['metadata']
                )
                for node in graph_data['nodes']
            ]
            
            edges = [
                GraphEdge(
                    source=edge['source'],
                    target=edge['target'],
                    label=edge['label'],
                    weight=edge['weight'],
                    color=edge['color'],
                    metadata=edge['metadata']
                )
                for edge in graph_data['edges']
            ]
            
            # Generate export data based on format
            if format == 'json':
                export_data = visualizer.export_to_json(nodes, edges)
                mimetype = 'application/json'
                filename = f"maltego_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                
            elif format == 'graphml':
                export_data = visualizer.export_to_graphml(nodes, edges)
                mimetype = 'application/xml'
                filename = f"maltego_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.graphml"
                
            else:
                return jsonify({'error': f'Unsupported export format: {format}'}), 400
            
            # Create temporary file
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=f'.{format}') as f:
                f.write(export_data)
                temp_path = f.name
            
            return send_file(
                temp_path,
                as_attachment=True,
                download_name=filename,
                mimetype=mimetype
            )
            
        except Exception as e:
            logging.error(f"Export failed: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/status')
    def status():
        """Get application status"""
        return jsonify({
            'status': 'running',
            'version': config.app.version,
            'available_transforms': transform_registry.get_transform_names(),
            'available_datasources': datasource_manager.get_available_sources(),
            'timestamp': datetime.now().isoformat()
        })
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': 'Not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({'error': 'Internal server error'}), 500
    
    return app


if __name__ == '__main__':
    app = create_app()
    config = Config.load_from_file()
    app.run(
        host=config.app.host,
        port=config.app.port,
        debug=config.app.debug
    )
