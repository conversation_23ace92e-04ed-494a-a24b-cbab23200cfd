#!/usr/bin/env python3
"""
Maltego People Connection Analyzer
Main application entry point
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.web.app import create_app
from src.core.config import Config


def setup_logging(level: str = "INFO"):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/maltego.log')
        ]
    )


def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(description='Maltego People Connection Analyzer')
    parser.add_argument('--config', '-c', default='config/config.yaml',
                       help='Configuration file path (default: config/config.yaml)')
    parser.add_argument('--host', default=None,
                       help='Host to bind to (overrides config)')
    parser.add_argument('--port', type=int, default=None,
                       help='Port to bind to (overrides config)')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode (overrides config)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    try:
        # Load configuration
        config = Config.load_from_file(args.config)
        
        # Override config with command line arguments
        if args.host:
            config.app.host = args.host
        if args.port:
            config.app.port = args.port
        if args.debug:
            config.app.debug = True
        
        # Ensure required directories exist
        config.ensure_directories()
        
        logger.info(f"Starting Maltego Analyzer v{config.app.version}")
        logger.info(f"Configuration loaded from: {args.config}")
        logger.info(f"Server will start on {config.app.host}:{config.app.port}")
        
        # Create and run Flask app
        app = create_app(args.config)
        
        logger.info("Application initialized successfully")
        logger.info("Starting web server...")
        
        app.run(
            host=config.app.host,
            port=config.app.port,
            debug=config.app.debug,
            threaded=True
        )
        
    except FileNotFoundError as e:
        logger.error(f"Configuration file not found: {e}")
        logger.error("Please create a configuration file or use --config to specify a different path")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        if args.debug or args.log_level == 'DEBUG':
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
