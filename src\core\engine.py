"""
Core Transform Engine for Maltego
Handles the execution and coordination of transforms
"""

import logging
import asyncio
import time
from typing import Dict, List, Any, Optional, Type
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from enum import Enum

from .config import Config
from .cache import CacheManager
from .rate_limiter import RateLimiter
from ..entities.base import Entity
from ..transforms.base import BaseTransform


class TransformStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"


@dataclass
class TransformResult:
    """Result of a transform execution"""
    transform_name: str
    input_entity: Entity
    output_entities: List[Entity]
    status: TransformStatus
    execution_time: float
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None


class TransformEngine:
    """Core engine for executing transforms and managing the analysis pipeline"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.cache = CacheManager(config.cache)
        self.rate_limiter = RateLimiter(config.transforms.rate_limit)
        self.transforms: Dict[str, BaseTransform] = {}
        self.executor = ThreadPoolExecutor(max_workers=config.performance.max_workers)
        
    def register_transform(self, transform: BaseTransform):
        """Register a transform with the engine"""
        self.transforms[transform.name] = transform
        self.logger.info(f"Registered transform: {transform.name}")
        
    def get_available_transforms(self, entity_type: str = None) -> List[str]:
        """Get list of available transforms, optionally filtered by entity type"""
        if entity_type:
            return [
                name for name, transform in self.transforms.items()
                if entity_type in transform.input_types
            ]
        return list(self.transforms.keys())
        
    def execute_transform(self, transform_name: str, entity: Entity) -> TransformResult:
        """Execute a single transform on an entity"""
        start_time = time.time()
        
        if transform_name not in self.transforms:
            return TransformResult(
                transform_name=transform_name,
                input_entity=entity,
                output_entities=[],
                status=TransformStatus.FAILED,
                execution_time=0,
                error_message=f"Transform '{transform_name}' not found"
            )
            
        transform = self.transforms[transform_name]
        
        # Check if entity type is supported
        if entity.type not in transform.input_types:
            return TransformResult(
                transform_name=transform_name,
                input_entity=entity,
                output_entities=[],
                status=TransformStatus.FAILED,
                execution_time=0,
                error_message=f"Entity type '{entity.type}' not supported by transform"
            )
            
        # Check cache first
        cache_key = f"{transform_name}:{entity.value}:{entity.type}"
        cached_result = self.cache.get(cache_key)
        if cached_result:
            self.logger.debug(f"Cache hit for {cache_key}")
            return cached_result
            
        # Apply rate limiting
        self.rate_limiter.wait_if_needed()
        
        try:
            self.logger.info(f"Executing transform '{transform_name}' on {entity.type}: {entity.value}")
            
            # Execute the transform
            output_entities = transform.execute(entity)
            execution_time = time.time() - start_time
            
            result = TransformResult(
                transform_name=transform_name,
                input_entity=entity,
                output_entities=output_entities,
                status=TransformStatus.COMPLETED,
                execution_time=execution_time
            )
            
            # Cache the result
            self.cache.set(cache_key, result)
            
            self.logger.info(f"Transform completed: {len(output_entities)} entities found")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = str(e)
            self.logger.error(f"Transform failed: {error_msg}")
            
            return TransformResult(
                transform_name=transform_name,
                input_entity=entity,
                output_entities=[],
                status=TransformStatus.FAILED,
                execution_time=execution_time,
                error_message=error_msg
            )
            
    def execute_transforms_parallel(self, transform_names: List[str], entity: Entity) -> List[TransformResult]:
        """Execute multiple transforms in parallel on an entity"""
        futures = []
        
        for transform_name in transform_names:
            future = self.executor.submit(self.execute_transform, transform_name, entity)
            futures.append(future)
            
        results = []
        for future in as_completed(futures, timeout=self.config.performance.transform_timeout):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                self.logger.error(f"Transform execution failed: {e}")
                
        return results
        
    def analyze_entity(self, entity: Entity, transform_names: List[str] = None, 
                      max_depth: int = 2) -> Dict[str, Any]:
        """
        Perform comprehensive analysis of an entity
        
        Args:
            entity: The input entity to analyze
            transform_names: List of specific transforms to run (if None, runs all applicable)
            max_depth: Maximum depth of recursive analysis
            
        Returns:
            Dictionary containing analysis results and graph data
        """
        if transform_names is None:
            transform_names = self.get_available_transforms(entity.type)
            
        analysis_results = {
            'input_entity': entity,
            'transforms': [],
            'entities': {entity.value: entity},
            'connections': [],
            'metadata': {
                'start_time': time.time(),
                'max_depth': max_depth,
                'total_entities': 1,
                'total_connections': 0
            }
        }
        
        # Queue for breadth-first analysis
        analysis_queue = [(entity, 0)]  # (entity, depth)
        processed_entities = {entity.value}
        
        while analysis_queue and len(analysis_queue) > 0:
            current_entity, depth = analysis_queue.pop(0)
            
            if depth >= max_depth:
                continue
                
            # Get applicable transforms for current entity
            applicable_transforms = self.get_available_transforms(current_entity.type)
            if transform_names:
                applicable_transforms = [t for t in applicable_transforms if t in transform_names]
                
            # Execute transforms
            transform_results = self.execute_transforms_parallel(applicable_transforms, current_entity)
            
            for result in transform_results:
                analysis_results['transforms'].append(result)
                
                # Process output entities
                for output_entity in result.output_entities:
                    entity_key = f"{output_entity.type}:{output_entity.value}"
                    
                    # Add entity if not already processed
                    if entity_key not in processed_entities:
                        analysis_results['entities'][entity_key] = output_entity
                        processed_entities.add(entity_key)
                        
                        # Add to queue for further analysis if within depth limit
                        if depth + 1 < max_depth:
                            analysis_queue.append((output_entity, depth + 1))
                            
                    # Add connection
                    connection = {
                        'source': f"{current_entity.type}:{current_entity.value}",
                        'target': entity_key,
                        'transform': result.transform_name,
                        'confidence': getattr(output_entity, 'confidence', 1.0),
                        'metadata': getattr(output_entity, 'metadata', {})
                    }
                    analysis_results['connections'].append(connection)
                    
        # Update metadata
        analysis_results['metadata'].update({
            'end_time': time.time(),
            'total_entities': len(analysis_results['entities']),
            'total_connections': len(analysis_results['connections']),
            'execution_time': time.time() - analysis_results['metadata']['start_time']
        })
        
        self.logger.info(f"Analysis completed: {analysis_results['metadata']['total_entities']} entities, "
                        f"{analysis_results['metadata']['total_connections']} connections")
        
        return analysis_results
        
    def shutdown(self):
        """Shutdown the engine and cleanup resources"""
        self.executor.shutdown(wait=True)
        self.cache.close()
        self.logger.info("Transform engine shutdown complete")
