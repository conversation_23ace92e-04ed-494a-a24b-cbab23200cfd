/* Custom styles for Maltego Analyzer */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-bg: #f8f9fa;
    --dark-bg: #343a40;
}

body {
    background-color: var(--light-bg);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.3rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    transition: color 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: white;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: white;
    border-bottom: 2px solid var(--light-bg);
    border-radius: 10px 10px 0 0 !important;
    padding: 1rem 1.25rem;
}

.card-title {
    color: var(--primary-color);
    font-weight: 600;
}

/* Forms */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-check-input:checked {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
    transform: translateY(-1px);
}

.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* Graph Container */
#graphContainer {
    border-radius: 8px;
    background-color: white;
    position: relative;
}

#graphContainer .plotly-graph-div {
    border-radius: 8px;
}

/* Entity Details */
.accordion-button {
    border-radius: 8px !important;
    font-weight: 500;
}

.accordion-button:not(.collapsed) {
    background-color: var(--secondary-color);
    color: white;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 8px;
    font-weight: 500;
}

.alert-success {
    background-color: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-danger {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

/* Loading States */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Modal */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 2px solid var(--light-bg);
    border-radius: 10px 10px 0 0;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    #graphContainer {
        height: 400px !important;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 8px !important;
        margin-bottom: 0.25rem;
    }
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Entity type colors for consistency */
.entity-email { color: #e74c3c; }
.entity-domain { color: #3498db; }
.entity-ip { color: #f39c12; }
.entity-phone { color: #9b59b6; }
.entity-person { color: #2ecc71; }
.entity-organization { color: #34495e; }
.entity-social { color: #e67e22; }
.entity-url { color: #1abc9c; }
.entity-bitcoin { color: #f1c40f; }
.entity-location { color: #95a5a6; }

/* Utility classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.shadow-sm-custom {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.border-radius-lg {
    border-radius: 10px !important;
}

/* Print styles */
@media print {
    .navbar, .btn, .modal, #alertContainer {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    #graphContainer {
        height: auto !important;
        min-height: 400px;
    }
}
