"""
Configuration management for Maltego
"""

import os
import yaml
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from pathlib import Path


@dataclass
class AppConfig:
    name: str = "Maltego People Connection Analyzer"
    version: str = "1.0.0"
    debug: bool = False
    host: str = "localhost"
    port: int = 5000


@dataclass
class DatabaseConfig:
    type: str = "sqlite"
    path: str = "data/maltego.db"
    echo: bool = False


@dataclass
class CacheConfig:
    enabled: bool = True
    directory: str = "data/cache"
    ttl: int = 3600


@dataclass
class RateLimitConfig:
    enabled: bool = True
    requests_per_minute: int = 60


@dataclass
class TransformConfig:
    max_results: int = 50
    timeout: int = 30
    rate_limit: RateLimitConfig = field(default_factory=RateLimitConfig)
    enabled: List[str] = field(default_factory=list)


@dataclass
class VisualizationConfig:
    layout: str = "force"
    interactive: bool = True
    zoom: bool = True
    pan: bool = True


@dataclass
class LoggingConfig:
    level: str = "INFO"
    file: str = "data/logs/maltego.log"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    max_size: str = "10MB"
    backup_count: int = 5


@dataclass
class SecurityConfig:
    https: bool = False
    session_secret_key: str = "change-this-secret-key"
    session_timeout: int = 3600


@dataclass
class PerformanceConfig:
    max_workers: int = 10
    max_memory_mb: int = 1024
    transform_timeout: int = 300
    total_timeout: int = 1800


@dataclass
class Config:
    """Main configuration class"""
    app: AppConfig = field(default_factory=AppConfig)
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    transforms: TransformConfig = field(default_factory=TransformConfig)
    visualization: VisualizationConfig = field(default_factory=VisualizationConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    api_keys: Dict[str, Any] = field(default_factory=dict)
    datasources: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def load_from_file(cls, config_path: str = "config/config.yaml") -> "Config":
        """Load configuration from YAML file"""
        config_file = Path(config_path)
        
        if not config_file.exists():
            # Try to load from example file
            example_file = Path("config/config.example.yaml")
            if example_file.exists():
                print(f"Config file {config_path} not found, using example configuration")
                config_file = example_file
            else:
                print(f"No configuration file found, using defaults")
                return cls()
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                
            config = cls()
            
            # Load app config
            if 'app' in data:
                app_data = data['app']
                config.app = AppConfig(
                    name=app_data.get('name', config.app.name),
                    version=app_data.get('version', config.app.version),
                    debug=app_data.get('debug', config.app.debug),
                    host=app_data.get('host', config.app.host),
                    port=app_data.get('port', config.app.port)
                )
            
            # Load database config
            if 'database' in data:
                db_data = data['database']
                config.database = DatabaseConfig(
                    type=db_data.get('type', config.database.type),
                    path=db_data.get('path', config.database.path),
                    echo=db_data.get('echo', config.database.echo)
                )
            
            # Load cache config
            if 'cache' in data:
                cache_data = data['cache']
                config.cache = CacheConfig(
                    enabled=cache_data.get('enabled', config.cache.enabled),
                    directory=cache_data.get('directory', config.cache.directory),
                    ttl=cache_data.get('ttl', config.cache.ttl)
                )
            
            # Load transform config
            if 'transforms' in data:
                transform_data = data['transforms']
                rate_limit_data = transform_data.get('rate_limit', {})
                
                config.transforms = TransformConfig(
                    max_results=transform_data.get('max_results', config.transforms.max_results),
                    timeout=transform_data.get('timeout', config.transforms.timeout),
                    rate_limit=RateLimitConfig(
                        enabled=rate_limit_data.get('enabled', True),
                        requests_per_minute=rate_limit_data.get('requests_per_minute', 60)
                    ),
                    enabled=transform_data.get('enabled', [])
                )
            
            # Load visualization config
            if 'visualization' in data:
                viz_data = data['visualization']
                config.visualization = VisualizationConfig(
                    layout=viz_data.get('layout', config.visualization.layout),
                    interactive=viz_data.get('interactive', config.visualization.interactive),
                    zoom=viz_data.get('zoom', config.visualization.zoom),
                    pan=viz_data.get('pan', config.visualization.pan)
                )
            
            # Load logging config
            if 'logging' in data:
                log_data = data['logging']
                config.logging = LoggingConfig(
                    level=log_data.get('level', config.logging.level),
                    file=log_data.get('file', config.logging.file),
                    format=log_data.get('format', config.logging.format),
                    max_size=log_data.get('max_size', config.logging.max_size),
                    backup_count=log_data.get('backup_count', config.logging.backup_count)
                )
            
            # Load security config
            if 'security' in data:
                sec_data = data['security']
                session_data = sec_data.get('session', {})
                config.security = SecurityConfig(
                    https=sec_data.get('https', config.security.https),
                    session_secret_key=session_data.get('secret_key', config.security.session_secret_key),
                    session_timeout=session_data.get('timeout', config.security.session_timeout)
                )
            
            # Load performance config
            if 'performance' in data:
                perf_data = data['performance']
                config.performance = PerformanceConfig(
                    max_workers=perf_data.get('max_workers', config.performance.max_workers),
                    max_memory_mb=perf_data.get('max_memory_mb', config.performance.max_memory_mb),
                    transform_timeout=perf_data.get('transform_timeout', config.performance.transform_timeout),
                    total_timeout=perf_data.get('total_timeout', config.performance.total_timeout)
                )
            
            # Load API keys and datasources
            config.api_keys = data.get('api_keys', {})
            config.datasources = data.get('datasources', {})
            
            return config
            
        except Exception as e:
            print(f"Error loading configuration: {e}")
            print("Using default configuration")
            return cls()
    
    def get_api_key(self, service: str, key_name: str = 'api_key') -> Optional[str]:
        """Get API key for a service"""
        service_config = self.api_keys.get(service, {})
        api_key = service_config.get(key_name)
        
        # Also check environment variables
        if not api_key:
            env_var = f"{service.upper()}_{key_name.upper()}"
            api_key = os.getenv(env_var)
            
        return api_key
    
    def is_datasource_enabled(self, datasource: str) -> bool:
        """Check if a datasource is enabled"""
        return self.datasources.get(datasource, {}).get('enabled', False)
    
    def ensure_directories(self):
        """Ensure all required directories exist"""
        directories = [
            self.cache.directory,
            os.path.dirname(self.database.path),
            os.path.dirname(self.logging.file),
            "data/exports"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
