"""
Rate limiting for API calls and transforms
"""

import time
import threading
from collections import deque
from typing import Optional
from dataclasses import dataclass

from .config import RateLimitConfig


class RateLimiter:
    """Thread-safe rate limiter for API calls"""
    
    def __init__(self, config: RateLimitConfig):
        self.config = config
        self.requests = deque()
        self.lock = threading.Lock()
        
    def wait_if_needed(self) -> Optional[float]:
        """
        Wait if rate limit would be exceeded
        Returns the wait time in seconds, or None if no wait was needed
        """
        if not self.config.enabled:
            return None
            
        with self.lock:
            current_time = time.time()
            
            # Remove requests older than 1 minute
            while self.requests and current_time - self.requests[0] > 60:
                self.requests.popleft()
            
            # Check if we're at the limit
            if len(self.requests) >= self.config.requests_per_minute:
                # Calculate wait time until oldest request is 1 minute old
                wait_time = 60 - (current_time - self.requests[0])
                if wait_time > 0:
                    time.sleep(wait_time)
                    return wait_time
            
            # Record this request
            self.requests.append(current_time)
            return None
    
    def get_current_rate(self) -> int:
        """Get current number of requests in the last minute"""
        if not self.config.enabled:
            return 0
            
        with self.lock:
            current_time = time.time()
            
            # Remove requests older than 1 minute
            while self.requests and current_time - self.requests[0] > 60:
                self.requests.popleft()
                
            return len(self.requests)
    
    def reset(self):
        """Reset the rate limiter"""
        with self.lock:
            self.requests.clear()
