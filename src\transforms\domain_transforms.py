"""
Domain-related transforms
"""

from typing import List
from ..entities.base import Entity, EntityType
from ..datasources.base import DataSourceManager
from .base import BaseTransform


class DomainToWhoisTransform(BaseTransform):
    """Transform to get WHOIS information for a domain"""
    
    @property
    def name(self) -> str:
        return "domain_to_whois"
    
    @property
    def description(self) -> str:
        return "Get WHOIS information for a domain including registrant details and name servers"
    
    @property
    def input_types(self) -> List[str]:
        return [EntityType.DOMAIN.value]
    
    @property
    def output_types(self) -> List[str]:
        return [
            EntityType.EMAIL.value,
            EntityType.ORGANIZATION.value,
            EntityType.DOMAIN.value,
            EntityType.PERSON.value
        ]
    
    def execute(self, entity: Entity) -> List[Entity]:
        """Execute WHOIS lookup for domain"""
        if not self.validate_input(entity):
            return []
        
        # Get WHOIS data sources
        whois_sources = [
            source for source in self.datasource_manager.sources.values()
            if source.name == 'whois' and source.supports_entity_type(entity.type)
        ]
        
        all_entities = []
        
        for source in whois_sources:
            try:
                result = source.query(entity)
                if result.success:
                    all_entities.extend(result.entities)
                    self.logger.info(f"WHOIS lookup found {len(result.entities)} entities")
                else:
                    self.logger.warning(f"WHOIS lookup failed: {result.error_message}")
                    
            except Exception as e:
                self.logger.error(f"Error in WHOIS lookup: {e}")
        
        return self.filter_results(all_entities)


class DomainToDNSTransform(BaseTransform):
    """Transform to get DNS records for a domain"""
    
    @property
    def name(self) -> str:
        return "domain_to_dns"
    
    @property
    def description(self) -> str:
        return "Get DNS records for a domain including A, MX, NS, and other record types"
    
    @property
    def input_types(self) -> List[str]:
        return [EntityType.DOMAIN.value]
    
    @property
    def output_types(self) -> List[str]:
        return [
            EntityType.IP_ADDRESS.value,
            EntityType.DOMAIN.value
        ]
    
    def execute(self, entity: Entity) -> List[Entity]:
        """Execute DNS lookup for domain"""
        if not self.validate_input(entity):
            return []
        
        # Get DNS data sources
        dns_sources = [
            source for source in self.datasource_manager.sources.values()
            if source.name == 'dns' and source.supports_entity_type(entity.type)
        ]
        
        all_entities = []
        
        for source in dns_sources:
            try:
                result = source.query(entity)
                if result.success:
                    all_entities.extend(result.entities)
                    self.logger.info(f"DNS lookup found {len(result.entities)} entities")
                else:
                    self.logger.warning(f"DNS lookup failed: {result.error_message}")
                    
            except Exception as e:
                self.logger.error(f"Error in DNS lookup: {e}")
        
        return self.filter_results(all_entities)


class EmailToDomainTransform(BaseTransform):
    """Transform to extract domain from email address"""
    
    @property
    def name(self) -> str:
        return "email_to_domain"
    
    @property
    def description(self) -> str:
        return "Extract domain from email address"
    
    @property
    def input_types(self) -> List[str]:
        return [EntityType.EMAIL.value]
    
    @property
    def output_types(self) -> List[str]:
        return [EntityType.DOMAIN.value]
    
    def execute(self, entity: Entity) -> List[Entity]:
        """Extract domain from email"""
        if not self.validate_input(entity):
            return []
        
        if '@' not in entity.value:
            return []
        
        domain = entity.value.split('@')[1]
        
        domain_entity = Entity(
            type=EntityType.DOMAIN.value,
            value=domain,
            confidence=1.0,
            metadata={
                'source': 'email_extraction',
                'original_email': entity.value
            },
            source='email_to_domain'
        )
        
        return [domain_entity]


class DomainToSubdomainTransform(BaseTransform):
    """Transform to find subdomains of a domain"""
    
    @property
    def name(self) -> str:
        return "domain_to_subdomain"
    
    @property
    def description(self) -> str:
        return "Find common subdomains of a domain"
    
    @property
    def input_types(self) -> List[str]:
        return [EntityType.DOMAIN.value]
    
    @property
    def output_types(self) -> List[str]:
        return [EntityType.DOMAIN.value]
    
    def execute(self, entity: Entity) -> List[Entity]:
        """Find subdomains by trying common subdomain names"""
        if not self.validate_input(entity):
            return []
        
        # Common subdomain names to try
        common_subdomains = [
            'www', 'mail', 'ftp', 'admin', 'api', 'blog', 'dev', 'test',
            'staging', 'app', 'portal', 'shop', 'store', 'support',
            'help', 'docs', 'cdn', 'static', 'assets', 'images'
        ]
        
        found_entities = []
        
        # Get DNS data sources for subdomain checking
        dns_sources = [
            source for source in self.datasource_manager.sources.values()
            if source.name == 'dns'
        ]
        
        if not dns_sources:
            return []
        
        dns_source = dns_sources[0]  # Use first available DNS source
        
        for subdomain in common_subdomains:
            subdomain_name = f"{subdomain}.{entity.value}"
            
            # Create temporary entity for DNS lookup
            temp_entity = Entity(
                type=EntityType.DOMAIN.value,
                value=subdomain_name,
                confidence=0.5
            )
            
            try:
                result = dns_source.query(temp_entity)
                if result.success and result.entities:
                    # If DNS lookup succeeds, the subdomain exists
                    subdomain_entity = Entity(
                        type=EntityType.DOMAIN.value,
                        value=subdomain_name,
                        confidence=0.8,
                        metadata={
                            'source': 'subdomain_enumeration',
                            'parent_domain': entity.value,
                            'subdomain_type': subdomain
                        },
                        source='domain_to_subdomain'
                    )
                    found_entities.append(subdomain_entity)
                    
            except Exception as e:
                self.logger.debug(f"Subdomain check failed for {subdomain_name}: {e}")
                continue
        
        return self.filter_results(found_entities)
