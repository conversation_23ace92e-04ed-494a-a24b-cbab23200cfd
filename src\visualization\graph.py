"""
Graph visualization for Maltego analysis results
"""

import json
import networkx as nx
import plotly.graph_objects as go
import plotly.express as px
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass

from ..entities.base import Entity, EntityType


@dataclass
class GraphNode:
    """Represents a node in the graph"""
    id: str
    label: str
    type: str
    size: int = 10
    color: str = "#1f77b4"
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class GraphEdge:
    """Represents an edge in the graph"""
    source: str
    target: str
    label: str = ""
    weight: float = 1.0
    color: str = "#999999"
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class GraphVisualizer:
    """Creates interactive graph visualizations of analysis results"""
    
    # Color scheme for different entity types
    ENTITY_COLORS = {
        EntityType.EMAIL.value: "#e74c3c",
        EntityType.DOMAIN.value: "#3498db",
        EntityType.IP_ADDRESS.value: "#f39c12",
        EntityType.PHONE_NUMBER.value: "#9b59b6",
        EntityType.PERSON.value: "#2ecc71",
        EntityType.ORGANIZATION.value: "#34495e",
        EntityType.SOCIAL_MEDIA.value: "#e67e22",
        EntityType.URL.value: "#1abc9c",
        EntityType.BITCOIN_ADDRESS.value: "#f1c40f",
        EntityType.LOCATION.value: "#95a5a6",
        EntityType.UNKNOWN.value: "#7f8c8d"
    }
    
    # Size mapping for different entity types
    ENTITY_SIZES = {
        EntityType.EMAIL.value: 15,
        EntityType.DOMAIN.value: 20,
        EntityType.IP_ADDRESS.value: 18,
        EntityType.PHONE_NUMBER.value: 12,
        EntityType.PERSON.value: 25,
        EntityType.ORGANIZATION.value: 22,
        EntityType.SOCIAL_MEDIA.value: 14,
        EntityType.URL.value: 16,
        EntityType.BITCOIN_ADDRESS.value: 13,
        EntityType.LOCATION.value: 19,
        EntityType.UNKNOWN.value: 10
    }
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.layout_algorithm = config.get('layout', 'force')
        
    def create_graph_from_analysis(self, analysis_results: Dict[str, Any]) -> Tuple[List[GraphNode], List[GraphEdge]]:
        """Create graph nodes and edges from analysis results"""
        nodes = []
        edges = []
        
        # Create nodes from entities
        for entity_key, entity in analysis_results['entities'].items():
            node = self._create_node_from_entity(entity)
            nodes.append(node)
        
        # Create edges from connections
        for connection in analysis_results['connections']:
            edge = self._create_edge_from_connection(connection)
            edges.append(edge)
        
        return nodes, edges
    
    def _create_node_from_entity(self, entity: Entity) -> GraphNode:
        """Create a graph node from an entity"""
        entity_type = entity.type
        
        return GraphNode(
            id=f"{entity_type}:{entity.value}",
            label=entity.value,
            type=entity_type,
            size=self.ENTITY_SIZES.get(entity_type, 10),
            color=self.ENTITY_COLORS.get(entity_type, "#7f8c8d"),
            metadata={
                'confidence': entity.confidence,
                'source': entity.source,
                'entity_metadata': entity.metadata
            }
        )
    
    def _create_edge_from_connection(self, connection: Dict[str, Any]) -> GraphEdge:
        """Create a graph edge from a connection"""
        return GraphEdge(
            source=connection['source'],
            target=connection['target'],
            label=connection.get('transform', ''),
            weight=connection.get('confidence', 1.0),
            color="#999999",
            metadata=connection.get('metadata', {})
        )
    
    def create_networkx_graph(self, nodes: List[GraphNode], edges: List[GraphEdge]) -> nx.Graph:
        """Create a NetworkX graph from nodes and edges"""
        G = nx.Graph()
        
        # Add nodes
        for node in nodes:
            G.add_node(
                node.id,
                label=node.label,
                type=node.type,
                size=node.size,
                color=node.color,
                **node.metadata
            )
        
        # Add edges
        for edge in edges:
            G.add_edge(
                edge.source,
                edge.target,
                label=edge.label,
                weight=edge.weight,
                color=edge.color,
                **edge.metadata
            )
        
        return G
    
    def calculate_layout(self, G: nx.Graph) -> Dict[str, Tuple[float, float]]:
        """Calculate node positions using the specified layout algorithm"""
        if self.layout_algorithm == 'force':
            return nx.spring_layout(G, k=1, iterations=50)
        elif self.layout_algorithm == 'circular':
            return nx.circular_layout(G)
        elif self.layout_algorithm == 'hierarchical':
            return nx.nx_agraph.graphviz_layout(G, prog='dot') if hasattr(nx, 'nx_agraph') else nx.spring_layout(G)
        elif self.layout_algorithm == 'grid':
            return nx.grid_2d_graph(len(G.nodes), 1)
        else:
            return nx.spring_layout(G)
    
    def create_plotly_figure(self, nodes: List[GraphNode], edges: List[GraphEdge]) -> go.Figure:
        """Create an interactive Plotly figure from graph data"""
        G = self.create_networkx_graph(nodes, edges)
        pos = self.calculate_layout(G)
        
        # Extract node positions
        node_x = []
        node_y = []
        node_text = []
        node_colors = []
        node_sizes = []
        node_hover_text = []
        
        for node in nodes:
            if node.id in pos:
                x, y = pos[node.id]
                node_x.append(x)
                node_y.append(y)
                node_text.append(node.label)
                node_colors.append(node.color)
                node_sizes.append(node.size)
                
                # Create hover text
                hover_text = f"<b>{node.label}</b><br>"
                hover_text += f"Type: {node.type}<br>"
                hover_text += f"Confidence: {node.metadata.get('confidence', 'N/A')}<br>"
                if node.metadata.get('source'):
                    hover_text += f"Source: {node.metadata['source']}<br>"
                node_hover_text.append(hover_text)
        
        # Extract edge positions
        edge_x = []
        edge_y = []
        edge_text = []
        
        for edge in edges:
            if edge.source in pos and edge.target in pos:
                x0, y0 = pos[edge.source]
                x1, y1 = pos[edge.target]
                
                edge_x.extend([x0, x1, None])
                edge_y.extend([y0, y1, None])
                
                # Add edge label at midpoint
                if edge.label:
                    mid_x = (x0 + x1) / 2
                    mid_y = (y0 + y1) / 2
                    edge_text.append((mid_x, mid_y, edge.label))
        
        # Create edge trace
        edge_trace = go.Scatter(
            x=edge_x,
            y=edge_y,
            line=dict(width=1, color='#888'),
            hoverinfo='none',
            mode='lines'
        )
        
        # Create node trace
        node_trace = go.Scatter(
            x=node_x,
            y=node_y,
            mode='markers+text',
            hoverinfo='text',
            hovertext=node_hover_text,
            text=node_text,
            textposition="middle center",
            marker=dict(
                size=node_sizes,
                color=node_colors,
                line=dict(width=2, color='white'),
                opacity=0.8
            )
        )
        
        # Create edge label traces
        edge_label_traces = []
        for x, y, label in edge_text:
            edge_label_traces.append(
                go.Scatter(
                    x=[x],
                    y=[y],
                    mode='text',
                    text=[label],
                    textfont=dict(size=10, color='#666'),
                    showlegend=False,
                    hoverinfo='none'
                )
            )
        
        # Create figure
        fig = go.Figure(data=[edge_trace, node_trace] + edge_label_traces)
        
        # Update layout
        fig.update_layout(
            title="Maltego Connection Analysis",
            titlefont_size=16,
            showlegend=False,
            hovermode='closest',
            margin=dict(b=20, l=5, r=5, t=40),
            annotations=[
                dict(
                    text="Drag to pan, scroll to zoom",
                    showarrow=False,
                    xref="paper", yref="paper",
                    x=0.005, y=-0.002,
                    xanchor='left', yanchor='bottom',
                    font=dict(color='#888', size=12)
                )
            ],
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            plot_bgcolor='white'
        )
        
        return fig
    
    def export_to_json(self, nodes: List[GraphNode], edges: List[GraphEdge]) -> str:
        """Export graph data to JSON format"""
        graph_data = {
            'nodes': [
                {
                    'id': node.id,
                    'label': node.label,
                    'type': node.type,
                    'size': node.size,
                    'color': node.color,
                    'metadata': node.metadata
                }
                for node in nodes
            ],
            'edges': [
                {
                    'source': edge.source,
                    'target': edge.target,
                    'label': edge.label,
                    'weight': edge.weight,
                    'color': edge.color,
                    'metadata': edge.metadata
                }
                for edge in edges
            ]
        }
        
        return json.dumps(graph_data, indent=2)
    
    def export_to_graphml(self, nodes: List[GraphNode], edges: List[GraphEdge]) -> str:
        """Export graph to GraphML format for use in other tools"""
        G = self.create_networkx_graph(nodes, edges)
        
        # Convert to GraphML string
        import io
        buffer = io.StringIO()
        nx.write_graphml(G, buffer)
        return buffer.getvalue()
    
    def get_graph_statistics(self, nodes: List[GraphNode], edges: List[GraphEdge]) -> Dict[str, Any]:
        """Calculate graph statistics"""
        G = self.create_networkx_graph(nodes, edges)
        
        # Entity type distribution
        entity_types = {}
        for node in nodes:
            entity_types[node.type] = entity_types.get(node.type, 0) + 1
        
        # Connection statistics
        connection_types = {}
        for edge in edges:
            conn_type = edge.label or 'unknown'
            connection_types[conn_type] = connection_types.get(conn_type, 0) + 1
        
        stats = {
            'total_nodes': len(nodes),
            'total_edges': len(edges),
            'entity_types': entity_types,
            'connection_types': connection_types,
            'density': nx.density(G),
            'connected_components': nx.number_connected_components(G),
            'average_clustering': nx.average_clustering(G) if len(G) > 0 else 0
        }
        
        # Add centrality measures for top nodes
        if len(G) > 0:
            degree_centrality = nx.degree_centrality(G)
            betweenness_centrality = nx.betweenness_centrality(G)
            
            # Get top 5 nodes by degree centrality
            top_degree = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)[:5]
            top_betweenness = sorted(betweenness_centrality.items(), key=lambda x: x[1], reverse=True)[:5]
            
            stats['top_degree_centrality'] = top_degree
            stats['top_betweenness_centrality'] = top_betweenness
        
        return stats
