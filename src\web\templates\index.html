{% extends "base.html" %}

{% block content %}
<div class="row">
    <!-- Input Panel -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-search me-2"></i>
                    Analysis Input
                </h5>
            </div>
            <div class="card-body">
                <form id="analysisForm">
                    <div class="mb-3">
                        <label for="inputValue" class="form-label">Target Information</label>
                        <input type="text" class="form-control" id="inputValue" 
                               placeholder="Enter email, domain, IP, phone number..." required>
                        <div class="form-text">
                            Examples: <EMAIL>, example.com, ***********
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="entityType" class="form-label">Entity Type (Optional)</label>
                        <select class="form-select" id="entityType">
                            <option value="">Auto-detect</option>
                            <option value="email">Email</option>
                            <option value="domain">Domain</option>
                            <option value="ip_address">IP Address</option>
                            <option value="phone_number">Phone Number</option>
                            <option value="person">Person</option>
                            <option value="organization">Organization</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="maxDepth" class="form-label">Analysis Depth</label>
                        <select class="form-select" id="maxDepth">
                            <option value="1">Level 1 (Direct connections)</option>
                            <option value="2" selected>Level 2 (Recommended)</option>
                            <option value="3">Level 3 (Deep analysis)</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Transforms to Run</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="selectAllTransforms" checked>
                            <label class="form-check-label" for="selectAllTransforms">
                                <strong>Select All</strong>
                            </label>
                        </div>
                        <hr>
                        <div id="transformsList">
                            {% for transform in available_transforms %}
                            <div class="form-check">
                                <input class="form-check-input transform-checkbox" type="checkbox" 
                                       id="transform_{{ transform }}" value="{{ transform }}" checked>
                                <label class="form-check-label" for="transform_{{ transform }}">
                                    {{ transform.replace('_', ' ').title() }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-play me-2"></i>
                        Start Analysis
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Results Summary -->
        <div class="card mt-3" id="summaryCard" style="display: none;">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Analysis Summary
                </h6>
            </div>
            <div class="card-body">
                <div id="summaryContent"></div>
            </div>
        </div>
    </div>
    
    <!-- Visualization Panel -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-project-diagram me-2"></i>
                    Connection Graph
                </h5>
                <div class="btn-group" id="exportButtons" style="display: none;">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportData('json')">
                        <i class="fas fa-download me-1"></i>JSON
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportData('graphml')">
                        <i class="fas fa-download me-1"></i>GraphML
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="graphContainer" style="height: 600px;">
                    <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                        <div class="text-center">
                            <i class="fas fa-project-diagram fa-3x mb-3"></i>
                            <h5>No Analysis Yet</h5>
                            <p>Enter target information and click "Start Analysis" to begin</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Entity Details -->
        <div class="card mt-3" id="detailsCard" style="display: none;">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Entity Details
                </h6>
            </div>
            <div class="card-body">
                <div id="detailsContent"></div>
            </div>
        </div>
    </div>
</div>

<!-- Alert Container -->
<div id="alertContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>
{% endblock %}

{% block scripts %}
<script>
// Global variables
let currentAnalysisData = null;

// Form submission
document.getElementById('analysisForm').addEventListener('submit', function(e) {
    e.preventDefault();
    startAnalysis();
});

// Select all transforms checkbox
document.getElementById('selectAllTransforms').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.transform-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

// Individual transform checkboxes
document.querySelectorAll('.transform-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const selectAll = document.getElementById('selectAllTransforms');
        const allChecked = document.querySelectorAll('.transform-checkbox:checked').length === 
                          document.querySelectorAll('.transform-checkbox').length;
        selectAll.checked = allChecked;
    });
});

function startAnalysis() {
    const inputValue = document.getElementById('inputValue').value.trim();
    const entityType = document.getElementById('entityType').value;
    const maxDepth = parseInt(document.getElementById('maxDepth').value);
    
    // Get selected transforms
    const selectedTransforms = Array.from(document.querySelectorAll('.transform-checkbox:checked'))
        .map(cb => cb.value);
    
    if (!inputValue) {
        showAlert('Please enter target information', 'warning');
        return;
    }
    
    if (selectedTransforms.length === 0) {
        showAlert('Please select at least one transform', 'warning');
        return;
    }
    
    // Show loading modal
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();
    
    // Prepare request data
    const requestData = {
        input: inputValue,
        type: entityType || null,
        transforms: selectedTransforms,
        max_depth: maxDepth
    };
    
    // Make API request
    fetch('/api/analyze', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();
        
        if (data.success) {
            currentAnalysisData = data;
            displayResults(data);
            showAlert('Analysis completed successfully!', 'success');
        } else {
            showAlert('Analysis failed: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        loadingModal.hide();
        console.error('Error:', error);
        showAlert('Analysis failed: ' + error.message, 'danger');
    });
}

function displayResults(data) {
    // Display summary
    displaySummary(data.statistics);
    
    // Display graph
    displayGraph(data.graph_data);
    
    // Display entity details
    displayEntityDetails(data.entities);
    
    // Show export buttons
    document.getElementById('exportButtons').style.display = 'block';
}

function displaySummary(stats) {
    const summaryCard = document.getElementById('summaryCard');
    const summaryContent = document.getElementById('summaryContent');
    
    let html = `
        <div class="row text-center">
            <div class="col-6">
                <h4 class="text-primary">${stats.total_nodes}</h4>
                <small class="text-muted">Entities</small>
            </div>
            <div class="col-6">
                <h4 class="text-success">${stats.total_connections}</h4>
                <small class="text-muted">Connections</small>
            </div>
        </div>
        <hr>
        <h6>Entity Types:</h6>
        <ul class="list-unstyled">
    `;
    
    for (const [type, count] of Object.entries(stats.entity_types)) {
        html += `<li><span class="badge bg-secondary me-2">${count}</span>${type.replace('_', ' ')}</li>`;
    }
    
    html += '</ul>';
    
    summaryContent.innerHTML = html;
    summaryCard.style.display = 'block';
}

function displayGraph(graphData) {
    // Create Plotly visualization
    fetch('/api/visualize', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ graph_data: graphData })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const plotData = JSON.parse(data.plot_json);
            Plotly.newPlot('graphContainer', plotData.data, plotData.layout, {
                responsive: true,
                displayModeBar: true
            });
        }
    })
    .catch(error => {
        console.error('Visualization error:', error);
        showAlert('Failed to create visualization', 'warning');
    });
}

function displayEntityDetails(entities) {
    const detailsCard = document.getElementById('detailsCard');
    const detailsContent = document.getElementById('detailsContent');
    
    let html = '<div class="accordion" id="entityAccordion">';
    
    let index = 0;
    for (const [key, entity] of Object.entries(entities)) {
        html += `
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading${index}">
                    <button class="accordion-button collapsed" type="button" 
                            data-bs-toggle="collapse" data-bs-target="#collapse${index}">
                        <strong>${entity.type}:</strong>&nbsp;${entity.value}
                        <span class="badge bg-primary ms-2">${(entity.confidence * 100).toFixed(0)}%</span>
                    </button>
                </h2>
                <div id="collapse${index}" class="accordion-collapse collapse" 
                     data-bs-parent="#entityAccordion">
                    <div class="accordion-body">
                        <p><strong>Type:</strong> ${entity.type}</p>
                        <p><strong>Confidence:</strong> ${(entity.confidence * 100).toFixed(1)}%</p>
                        ${entity.source ? `<p><strong>Source:</strong> ${entity.source}</p>` : ''}
                        ${Object.keys(entity.metadata).length > 0 ? 
                          `<p><strong>Metadata:</strong></p><pre class="small">${JSON.stringify(entity.metadata, null, 2)}</pre>` : ''}
                    </div>
                </div>
            </div>
        `;
        index++;
    }
    
    html += '</div>';
    
    detailsContent.innerHTML = html;
    detailsCard.style.display = 'block';
}

function exportData(format) {
    if (!currentAnalysisData) {
        showAlert('No analysis data to export', 'warning');
        return;
    }
    
    fetch(`/api/export/${format}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ graph_data: currentAnalysisData.graph_data })
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('Export failed');
    })
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `maltego_analysis_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        showAlert(`Data exported as ${format.toUpperCase()}`, 'success');
    })
    .catch(error => {
        console.error('Export error:', error);
        showAlert('Export failed', 'danger');
    });
}

function showAlert(message, type) {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert_' + Date.now();
    
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" id="${alertId}" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHtml);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}

function showStatus() {
    const statusModal = new bootstrap.Modal(document.getElementById('statusModal'));
    statusModal.show();
    
    fetch('/api/status')
        .then(response => response.json())
        .then(data => {
            const statusContent = document.getElementById('statusContent');
            statusContent.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Application Status</h6>
                        <p><strong>Status:</strong> <span class="badge bg-success">${data.status}</span></p>
                        <p><strong>Version:</strong> ${data.version}</p>
                        <p><strong>Timestamp:</strong> ${new Date(data.timestamp).toLocaleString()}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Available Components</h6>
                        <p><strong>Transforms:</strong> ${data.available_transforms.length}</p>
                        <p><strong>Data Sources:</strong> ${data.available_datasources.length}</p>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Available Transforms</h6>
                        <ul class="list-unstyled small">
                            ${data.available_transforms.map(t => `<li>• ${t}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Available Data Sources</h6>
                        <ul class="list-unstyled small">
                            ${data.available_datasources.map(d => `<li>• ${d}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        })
        .catch(error => {
            document.getElementById('statusContent').innerHTML = 
                '<div class="alert alert-danger">Failed to load status information</div>';
        });
}
</script>
{% endblock %}
