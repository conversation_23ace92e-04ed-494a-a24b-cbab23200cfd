"""
Base entity classes for Maltego
"""

import re
import validators
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from enum import Enum


class EntityType(Enum):
    """Supported entity types"""
    EMAIL = "email"
    DOMAIN = "domain"
    IP_ADDRESS = "ip_address"
    PHONE_NUMBER = "phone_number"
    PERSON = "person"
    ORGANIZATION = "organization"
    SOCIAL_MEDIA = "social_media"
    URL = "url"
    BITCOIN_ADDRESS = "bitcoin_address"
    LOCATION = "location"
    UNKNOWN = "unknown"


@dataclass
class Entity:
    """Base entity class"""
    type: str
    value: str
    confidence: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    source: Optional[str] = None
    
    def __post_init__(self):
        """Validate entity after initialization"""
        if not self.value:
            raise ValueError("Entity value cannot be empty")
        
        if not 0 <= self.confidence <= 1:
            raise ValueError("Confidence must be between 0 and 1")
    
    def __str__(self) -> str:
        return f"{self.type}:{self.value}"
    
    def __hash__(self) -> int:
        return hash((self.type, self.value))
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, Entity):
            return False
        return self.type == other.type and self.value == other.value
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert entity to dictionary"""
        return {
            'type': self.type,
            'value': self.value,
            'confidence': self.confidence,
            'metadata': self.metadata,
            'source': self.source
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Entity':
        """Create entity from dictionary"""
        return cls(
            type=data['type'],
            value=data['value'],
            confidence=data.get('confidence', 1.0),
            metadata=data.get('metadata', {}),
            source=data.get('source')
        )


class EntityRecognizer:
    """Recognizes and classifies entity types from input strings"""
    
    # Regex patterns for entity recognition
    PATTERNS = {
        EntityType.EMAIL: re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
        EntityType.IP_ADDRESS: re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'),
        EntityType.PHONE_NUMBER: re.compile(r'(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'),
        EntityType.BITCOIN_ADDRESS: re.compile(r'\b[13][a-km-zA-HJ-NP-Z1-9]{25,34}\b|bc1[a-z0-9]{39,59}\b'),
        EntityType.URL: re.compile(r'https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:\w*))?)?'),
    }
    
    @classmethod
    def recognize_type(cls, value: str) -> EntityType:
        """Recognize entity type from value"""
        value = value.strip()
        
        # Check specific patterns
        for entity_type, pattern in cls.PATTERNS.items():
            if pattern.match(value):
                return entity_type
        
        # Additional validation using validators library
        if validators.email(value):
            return EntityType.EMAIL
        
        if validators.domain(value):
            return EntityType.DOMAIN
        
        if validators.ipv4(value) or validators.ipv6(value):
            return EntityType.IP_ADDRESS
        
        if validators.url(value):
            return EntityType.URL
        
        # Check if it looks like a person name (simple heuristic)
        if cls._looks_like_person_name(value):
            return EntityType.PERSON
        
        # Check if it looks like an organization
        if cls._looks_like_organization(value):
            return EntityType.ORGANIZATION
        
        return EntityType.UNKNOWN
    
    @classmethod
    def _looks_like_person_name(cls, value: str) -> bool:
        """Simple heuristic to detect person names"""
        # Basic checks for person names
        words = value.split()
        if len(words) < 2 or len(words) > 4:
            return False
        
        # Check if all words start with capital letter
        for word in words:
            if not word[0].isupper() or not word.isalpha():
                return False
        
        # Common name patterns
        if len(words) == 2:  # First Last
            return True
        elif len(words) == 3:  # First Middle Last or First Last Jr.
            return True
        
        return False
    
    @classmethod
    def _looks_like_organization(cls, value: str) -> bool:
        """Simple heuristic to detect organization names"""
        # Common organization suffixes
        org_suffixes = [
            'inc', 'corp', 'llc', 'ltd', 'company', 'co', 'corporation',
            'incorporated', 'limited', 'group', 'holdings', 'enterprises',
            'solutions', 'services', 'systems', 'technologies', 'tech'
        ]
        
        value_lower = value.lower()
        for suffix in org_suffixes:
            if value_lower.endswith(suffix) or f' {suffix}' in value_lower:
                return True
        
        return False
    
    @classmethod
    def create_entity(cls, value: str, entity_type: Optional[str] = None, 
                     confidence: float = 1.0, metadata: Optional[Dict[str, Any]] = None) -> Entity:
        """Create an entity with automatic type recognition if not specified"""
        if entity_type is None:
            recognized_type = cls.recognize_type(value)
            entity_type = recognized_type.value
        
        return Entity(
            type=entity_type,
            value=value,
            confidence=confidence,
            metadata=metadata or {}
        )
    
    @classmethod
    def extract_entities(cls, text: str) -> List[Entity]:
        """Extract all recognizable entities from text"""
        entities = []
        
        # Extract using patterns
        for entity_type, pattern in cls.PATTERNS.items():
            matches = pattern.finditer(text)
            for match in matches:
                entity = Entity(
                    type=entity_type.value,
                    value=match.group(),
                    confidence=0.9,  # Pattern-based recognition has high confidence
                    metadata={'extraction_method': 'pattern_matching'}
                )
                entities.append(entity)
        
        return entities


# Specific entity classes for different types
@dataclass
class EmailEntity(Entity):
    """Email entity with additional properties"""
    
    def __post_init__(self):
        super().__post_init__()
        if self.type != EntityType.EMAIL.value:
            self.type = EntityType.EMAIL.value
        
        # Extract domain from email
        if '@' in self.value:
            self.metadata['domain'] = self.value.split('@')[1]
            self.metadata['username'] = self.value.split('@')[0]


@dataclass
class DomainEntity(Entity):
    """Domain entity with additional properties"""
    
    def __post_init__(self):
        super().__post_init__()
        if self.type != EntityType.DOMAIN.value:
            self.type = EntityType.DOMAIN.value
        
        # Extract TLD
        parts = self.value.split('.')
        if len(parts) > 1:
            self.metadata['tld'] = parts[-1]
            self.metadata['subdomain'] = '.'.join(parts[:-2]) if len(parts) > 2 else None


@dataclass
class IPAddressEntity(Entity):
    """IP Address entity with additional properties"""
    
    def __post_init__(self):
        super().__post_init__()
        if self.type != EntityType.IP_ADDRESS.value:
            self.type = EntityType.IP_ADDRESS.value
        
        # Determine IP version
        if validators.ipv4(self.value):
            self.metadata['version'] = 'IPv4'
        elif validators.ipv6(self.value):
            self.metadata['version'] = 'IPv6'


@dataclass
class PersonEntity(Entity):
    """Person entity with additional properties"""
    
    def __post_init__(self):
        super().__post_init__()
        if self.type != EntityType.PERSON.value:
            self.type = EntityType.PERSON.value
        
        # Parse name components
        name_parts = self.value.split()
        if len(name_parts) >= 2:
            self.metadata['first_name'] = name_parts[0]
            self.metadata['last_name'] = name_parts[-1]
            if len(name_parts) > 2:
                self.metadata['middle_names'] = ' '.join(name_parts[1:-1])


@dataclass
class OrganizationEntity(Entity):
    """Organization entity with additional properties"""
    
    def __post_init__(self):
        super().__post_init__()
        if self.type != EntityType.ORGANIZATION.value:
            self.type = EntityType.ORGANIZATION.value
