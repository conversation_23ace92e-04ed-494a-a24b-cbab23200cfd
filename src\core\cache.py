"""
Cache management for Maltego transforms
"""

import os
import pickle
import time
import hashlib
from pathlib import Path
from typing import Any, Optional
from dataclasses import dataclass

from .config import CacheConfig


@dataclass
class CacheEntry:
    """Cache entry with data and metadata"""
    data: Any
    timestamp: float
    ttl: int
    
    def is_expired(self) -> bool:
        """Check if cache entry has expired"""
        return time.time() - self.timestamp > self.ttl


class CacheManager:
    """File-based cache manager for transform results"""
    
    def __init__(self, config: CacheConfig):
        self.config = config
        self.cache_dir = Path(config.directory)
        
        if config.enabled:
            self.cache_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_cache_path(self, key: str) -> Path:
        """Get file path for cache key"""
        # Create a hash of the key to avoid filesystem issues
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.cache"
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        if not self.config.enabled:
            return None
            
        cache_path = self._get_cache_path(key)
        
        if not cache_path.exists():
            return None
            
        try:
            with open(cache_path, 'rb') as f:
                entry: CacheEntry = pickle.load(f)
                
            if entry.is_expired():
                # Remove expired entry
                cache_path.unlink()
                return None
                
            return entry.data
            
        except Exception:
            # If there's any error reading the cache, remove the file
            try:
                cache_path.unlink()
            except:
                pass
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache"""
        if not self.config.enabled:
            return False
            
        cache_path = self._get_cache_path(key)
        
        if ttl is None:
            ttl = self.config.ttl
            
        entry = CacheEntry(
            data=value,
            timestamp=time.time(),
            ttl=ttl
        )
        
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(entry, f)
            return True
        except Exception:
            return False
    
    def delete(self, key: str) -> bool:
        """Delete value from cache"""
        if not self.config.enabled:
            return False
            
        cache_path = self._get_cache_path(key)
        
        try:
            if cache_path.exists():
                cache_path.unlink()
            return True
        except Exception:
            return False
    
    def clear(self) -> bool:
        """Clear all cache entries"""
        if not self.config.enabled:
            return False
            
        try:
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink()
            return True
        except Exception:
            return False
    
    def cleanup_expired(self) -> int:
        """Remove expired cache entries and return count of removed entries"""
        if not self.config.enabled:
            return 0
            
        removed_count = 0
        
        for cache_file in self.cache_dir.glob("*.cache"):
            try:
                with open(cache_file, 'rb') as f:
                    entry: CacheEntry = pickle.load(f)
                    
                if entry.is_expired():
                    cache_file.unlink()
                    removed_count += 1
                    
            except Exception:
                # If we can't read the file, remove it
                try:
                    cache_file.unlink()
                    removed_count += 1
                except:
                    pass
                    
        return removed_count
    
    def get_stats(self) -> dict:
        """Get cache statistics"""
        if not self.config.enabled:
            return {"enabled": False}
            
        total_files = 0
        total_size = 0
        expired_files = 0
        
        for cache_file in self.cache_dir.glob("*.cache"):
            try:
                total_files += 1
                total_size += cache_file.stat().st_size
                
                with open(cache_file, 'rb') as f:
                    entry: CacheEntry = pickle.load(f)
                    if entry.is_expired():
                        expired_files += 1
                        
            except Exception:
                expired_files += 1
                
        return {
            "enabled": True,
            "total_entries": total_files,
            "total_size_bytes": total_size,
            "expired_entries": expired_files,
            "cache_directory": str(self.cache_dir)
        }
    
    def close(self):
        """Cleanup cache manager"""
        # Cleanup expired entries on close
        if self.config.enabled:
            self.cleanup_expired()
