"""
WHOIS data source for domain and IP information
"""

import whois
import socket
from typing import List, Dict, Any
from datetime import datetime

from .base import BaseDataSource, DataSourceResult
from ..entities.base import Entity, EntityType, DomainEntity, IPAddressEntity, OrganizationEntity


class WhoisDataSource(BaseDataSource):
    """WHOIS data source for domain and IP lookups"""
    
    @property
    def name(self) -> str:
        return "whois"
    
    @property
    def supported_entity_types(self) -> List[str]:
        return [EntityType.DOMAIN.value, EntityType.IP_ADDRESS.value]
    
    def query(self, entity: Entity) -> DataSourceResult:
        """Query WHOIS information for domain or IP"""
        start_time = datetime.now()
        
        try:
            if entity.type == EntityType.DOMAIN.value:
                result = self._query_domain(entity)
            elif entity.type == EntityType.IP_ADDRESS.value:
                result = self._query_ip(entity)
            else:
                return DataSourceResult(
                    source=self.name,
                    data={},
                    entities=[],
                    success=False,
                    error_message=f"Unsupported entity type: {entity.type}"
                )
            
            response_time = (datetime.now() - start_time).total_seconds()
            result.response_time = response_time
            
            return result
            
        except Exception as e:
            response_time = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"WHOIS query failed for {entity.value}: {e}")
            
            return DataSourceResult(
                source=self.name,
                data={},
                entities=[],
                success=False,
                error_message=str(e),
                response_time=response_time
            )
    
    def _query_domain(self, entity: Entity) -> DataSourceResult:
        """Query WHOIS information for a domain"""
        domain_info = whois.whois(entity.value)
        
        # Extract relevant information
        whois_data = {}
        entities = []
        
        if domain_info:
            # Basic domain information
            whois_data = {
                'domain_name': domain_info.domain_name,
                'registrar': domain_info.registrar,
                'creation_date': self._format_date(domain_info.creation_date),
                'expiration_date': self._format_date(domain_info.expiration_date),
                'updated_date': self._format_date(domain_info.updated_date),
                'status': domain_info.status,
                'name_servers': domain_info.name_servers,
                'registrant_name': domain_info.name,
                'registrant_org': domain_info.org,
                'registrant_email': domain_info.emails,
                'registrant_country': domain_info.country,
                'registrant_state': domain_info.state,
                'registrant_city': domain_info.city,
                'registrant_address': domain_info.address
            }
            
            # Create entities from WHOIS data
            
            # Organization entity
            if domain_info.org:
                org_entity = OrganizationEntity(
                    type=EntityType.ORGANIZATION.value,
                    value=domain_info.org,
                    confidence=0.8,
                    metadata={'source': 'whois', 'role': 'registrant'},
                    source=self.name
                )
                entities.append(org_entity)
            
            # Email entities
            if domain_info.emails:
                emails = domain_info.emails if isinstance(domain_info.emails, list) else [domain_info.emails]
                for email in emails:
                    if email and '@' in email:
                        email_entity = Entity(
                            type=EntityType.EMAIL.value,
                            value=email,
                            confidence=0.9,
                            metadata={'source': 'whois', 'role': 'registrant_contact'},
                            source=self.name
                        )
                        entities.append(email_entity)
            
            # Name servers as domain entities
            if domain_info.name_servers:
                name_servers = domain_info.name_servers if isinstance(domain_info.name_servers, list) else [domain_info.name_servers]
                for ns in name_servers:
                    if ns:
                        ns_entity = DomainEntity(
                            type=EntityType.DOMAIN.value,
                            value=ns.lower(),
                            confidence=0.9,
                            metadata={'source': 'whois', 'role': 'name_server'},
                            source=self.name
                        )
                        entities.append(ns_entity)
        
        return DataSourceResult(
            source=self.name,
            data=whois_data,
            entities=entities,
            success=True
        )
    
    def _query_ip(self, entity: Entity) -> DataSourceResult:
        """Query WHOIS information for an IP address"""
        # For IP WHOIS, we'll use a simple reverse DNS lookup
        # In a production system, you'd want to use proper IP WHOIS services
        
        whois_data = {}
        entities = []
        
        try:
            # Reverse DNS lookup
            hostname = socket.gethostbyaddr(entity.value)[0]
            
            whois_data = {
                'ip_address': entity.value,
                'hostname': hostname,
                'reverse_dns': hostname
            }
            
            # Create domain entity from hostname
            if hostname and '.' in hostname:
                domain_entity = DomainEntity(
                    type=EntityType.DOMAIN.value,
                    value=hostname,
                    confidence=0.9,
                    metadata={'source': 'whois', 'role': 'reverse_dns'},
                    source=self.name
                )
                entities.append(domain_entity)
                
        except socket.herror:
            # No reverse DNS record
            whois_data = {
                'ip_address': entity.value,
                'hostname': None,
                'reverse_dns': None
            }
        
        return DataSourceResult(
            source=self.name,
            data=whois_data,
            entities=entities,
            success=True
        )
    
    def _format_date(self, date_value) -> str:
        """Format date value to string"""
        if not date_value:
            return None
            
        if isinstance(date_value, list):
            date_value = date_value[0]
            
        if isinstance(date_value, datetime):
            return date_value.isoformat()
        
        return str(date_value)
