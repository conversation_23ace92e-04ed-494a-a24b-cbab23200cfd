# Maltego People Connection Analyzer

A comprehensive tool for analyzing connections between people, organizations, domains, emails, phone numbers, IPs, social networks, and Bitcoin transactions using transform-based analysis similar to Maltego.

## 🚀 Features

- **Entity Recognition**: Automatically detect and classify different types of entities (emails, domains, IPs, etc.)
- **Transform Engine**: Modular system for running analysis transforms on entities
- **Data Source Integration**: Support for multiple data sources (WHOIS, DNS, social media APIs, etc.)
- **Interactive Graph Visualization**: Network graphs showing entity relationships with Plotly
- **Modern Web Interface**: User-friendly web application built with Flask and Bootstrap
- **Intelligent Caching**: Efficient caching system to avoid redundant API calls
- **Rate Limiting**: Built-in rate limiting to respect API limits
- **Multiple Export Formats**: Export results in JSON, GraphML, and CSV formats
- **Parallel Processing**: Concurrent transform execution for better performance

## 🏗️ Architecture

The system is built with a modular architecture consisting of:

### Core Components

- **Transform Engine** (`src/core/engine.py`): Orchestrates the execution of transforms with parallel processing
- **Entity System** (`src/entities/`): Handles entity recognition and classification with regex patterns
- **Data Sources** (`src/datasources/`): Abstraction layer for external data sources (WHOIS, DNS)
- **Transform Modules** (`src/transforms/`): Individual analysis transforms for different entity types
- **Visualization** (`src/visualization/`): Interactive graph creation with Plotly and NetworkX
- **Web Interface** (`src/web/`): Modern Flask-based web application with Bootstrap UI

### Supported Entity Types

- 📧 Email addresses
- 🌐 Domain names
- 🖥️ IP addresses
- 📱 Phone numbers
- 👤 Person names
- 🏢 Organizations
- 📱 Social media profiles
- 🔗 URLs
- ₿ Bitcoin addresses
- 📍 Geographic locations

### Available Transforms

- **Domain Analysis**: WHOIS lookup, DNS records, subdomain discovery
- **Email Analysis**: Domain extraction, email validation
- **IP Analysis**: Geolocation, reverse DNS, port scanning
- **Network Analysis**: Connection mapping, relationship discovery

## 📁 Directory Structure

```
maltego/
├── src/
│   ├── core/           # Core engine, configuration, caching, rate limiting
│   ├── entities/       # Entity recognition and classification
│   ├── datasources/    # Data source integrations (WHOIS, DNS)
│   ├── transforms/     # Transform implementations
│   │   ├── domain_transforms.py    # Domain-related transforms
│   │   └── ip_transforms.py        # IP-related transforms
│   ├── visualization/  # Interactive graph visualization
│   └── web/           # Flask web interface with templates and static files
├── config/            # Configuration files
├── logs/              # Application logs
├── cache/             # Cached transform results
├── exports/           # Exported analysis data
├── temp/              # Temporary files
├── requirements.txt   # Python dependencies
├── app.py            # Main application entry point
└── setup.py          # Automated setup script
```

## ⚡ Quick Start

### Automated Setup

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd maltego
   python setup.py
   ```

2. **Configure API keys** (optional):
   ```bash
   # Edit .env file with your API keys
   nano .env
   ```

3. **Start the application**:
   ```bash
   python app.py
   ```

4. **Open your browser** to `http://localhost:5000`

### Manual Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd maltego
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup configuration**:
   ```bash
   cp config/config.example.yaml config/config.yaml
   mkdir -p logs cache exports temp
   ```

4. **Run the application**:
   ```bash
   python app.py
   ```

## ⚙️ Configuration

### Basic Configuration

The application uses YAML configuration. Edit `config/config.yaml`:

```yaml
app:
  host: "127.0.0.1"
  port: 5000
  debug: false

transforms:
  max_depth: 3
  timeout: 30
  parallel_execution: true
  max_workers: 4

datasources:
  whois:
    enabled: true
    timeout: 10

  dns:
    enabled: true
    servers: ["*******", "*******"]
```

### API Keys (Optional)

For enhanced functionality, configure API keys in `.env`:

```bash
# Social Media APIs
TWITTER_API_KEY=your_twitter_api_key
LINKEDIN_CLIENT_ID=your_linkedin_client_id

# Search Engines
GOOGLE_API_KEY=your_google_api_key
BING_API_KEY=your_bing_api_key

# Threat Intelligence
VIRUSTOTAL_API_KEY=your_virustotal_api_key
```

## 🖥️ Usage

### Web Interface

1. **Start the application**: `python app.py`
2. **Navigate to**: `http://localhost:5000`
3. **Enter target**: Email, domain, IP address, etc.
4. **Select transforms**: Choose which analysis to run
5. **Set depth**: Control how deep the analysis goes
6. **View results**: Interactive graph with entity details
7. **Export data**: Download results in various formats

### Command Line Options

```bash
# Custom configuration
python app.py --config custom_config.yaml

# Debug mode
python app.py --debug --log-level DEBUG

# Custom host/port
python app.py --host 0.0.0.0 --port 8080
```

### API Endpoints

#### Analyze Entity
```http
POST /api/analyze
Content-Type: application/json

{
  "input": "<EMAIL>",
  "type": "email",
  "transforms": ["email_to_domain", "domain_to_whois"],
  "max_depth": 2
}
```

#### Get System Status
```http
GET /api/status
```

#### Export Analysis Results
```http
POST /api/export/json
Content-Type: application/json

{
  "graph_data": { "nodes": [...], "edges": [...] }
}
```

## 🔧 Development

### Adding Custom Transforms

1. **Create transform class**:
```python
from src.transforms.base import BaseTransform
from src.entities.base import Entity, EntityType

class MyCustomTransform(BaseTransform):
    @property
    def name(self) -> str:
        return "my_custom_transform"

    @property
    def description(self) -> str:
        return "Description of what this transform does"

    @property
    def input_types(self) -> List[str]:
        return [EntityType.EMAIL.value]

    @property
    def output_types(self) -> List[str]:
        return [EntityType.PERSON.value]

    def execute(self, entity: Entity) -> List[Entity]:
        # Your transform logic here
        results = []
        # ... processing ...
        return results
```

2. **Register the transform**:
```python
# In src/web/app.py
custom_transform = MyCustomTransform(config.transforms.__dict__, datasource_manager)
transform_registry.register(custom_transform)
engine.register_transform(custom_transform)
```

### Adding Data Sources

1. **Create data source class**:
```python
from src.datasources.base import BaseDataSource

class MyDataSource(BaseDataSource):
    @property
    def name(self) -> str:
        return "my_datasource"

    def query(self, entity: Entity) -> DataSourceResult:
        # Your data source logic
        pass

    def supports_entity_type(self, entity_type: str) -> bool:
        return entity_type in ["email", "domain"]
```

2. **Register the data source**:
```python
my_source = MyDataSource(config.datasources.get('my_datasource', {}))
datasource_manager.register_source(my_source)
```

## 🔒 Security & Privacy

### Security Features
- Input validation and sanitization
- Rate limiting to prevent abuse
- Secure session management
- HTTPS support for production
- API key protection with environment variables

### Privacy Considerations
- All data processing is local (no data sent to third parties without explicit API calls)
- Caching can be disabled for sensitive analyses
- Export controls for data protection
- Configurable data retention policies

### Ethical Use Guidelines
- **Legal Compliance**: Ensure all activities comply with local laws and regulations
- **Authorization**: Only analyze data you have permission to investigate
- **Responsible Disclosure**: Report security vulnerabilities responsibly
- **Privacy Respect**: Respect individual privacy and data protection rights

## 🧪 Testing

### Run Tests
```bash
# Basic functionality test
python setup.py

# Manual testing
python -c "
from src.entities.base import EntityRecognizer
entity = EntityRecognizer.create_entity('<EMAIL>')
print(f'Detected: {entity.type} - {entity.value}')
"
```

### Test Coverage
- Entity recognition for all supported types
- Transform execution and result validation
- Data source integration and error handling
- Web interface functionality
- Configuration loading and validation

## 📊 Performance

### Optimization Features
- **Parallel Processing**: Concurrent transform execution
- **Intelligent Caching**: Avoid redundant API calls
- **Rate Limiting**: Respect API limits automatically
- **Memory Management**: Efficient data structures
- **Lazy Loading**: Load data only when needed

### Performance Tips
- Enable caching for repeated analyses
- Use appropriate analysis depth (2-3 levels recommended)
- Configure rate limits based on your API quotas
- Monitor memory usage for large datasets

## 🤝 Contributing

1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/amazing-feature`
3. **Make changes** and add tests
4. **Commit changes**: `git commit -m 'Add amazing feature'`
5. **Push to branch**: `git push origin feature/amazing-feature`
6. **Submit pull request**

### Development Setup
```bash
# Clone your fork
git clone https://github.com/yourusername/maltego.git
cd maltego

# Install development dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt  # If available

# Run in development mode
python app.py --debug
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

**This tool is for educational and research purposes only.**

- Users are responsible for ensuring compliance with applicable laws
- Obtain proper authorization before analyzing third-party data
- Use responsibly and ethically
- The developers are not responsible for misuse of this tool

## 🆘 Support

- **Documentation**: Check this README and inline code comments
- **Issues**: Report bugs and feature requests via GitHub Issues
- **Discussions**: Join community discussions for help and ideas

---

**Built with ❤️ for the cybersecurity and OSINT community**
