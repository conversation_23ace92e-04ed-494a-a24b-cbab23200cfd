"""
DNS data source for domain resolution and DNS record lookups
"""

import dns.resolver
import dns.reversename
from typing import List, Dict, Any

from .base import BaseDataSource, DataSourceResult
from ..entities.base import Entity, EntityType, DomainEntity, IPAddressEntity


class DNSDataSource(BaseDataSource):
    """DNS data source for domain and IP lookups"""
    
    @property
    def name(self) -> str:
        return "dns"
    
    @property
    def supported_entity_types(self) -> List[str]:
        return [EntityType.DOMAIN.value, EntityType.IP_ADDRESS.value]
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # Configure DNS servers if specified
        dns_servers = config.get('servers', ['*******', '*******'])
        self.resolver = dns.resolver.Resolver()
        self.resolver.nameservers = dns_servers
        self.resolver.timeout = config.get('timeout', 10)
    
    def query(self, entity: Entity) -> DataSourceResult:
        """Query DNS information for domain or IP"""
        try:
            if entity.type == EntityType.DOMAIN.value:
                return self._query_domain(entity)
            elif entity.type == EntityType.IP_ADDRESS.value:
                return self._query_ip(entity)
            else:
                return DataSourceResult(
                    source=self.name,
                    data={},
                    entities=[],
                    success=False,
                    error_message=f"Unsupported entity type: {entity.type}"
                )
                
        except Exception as e:
            self.logger.error(f"DNS query failed for {entity.value}: {e}")
            return DataSourceResult(
                source=self.name,
                data={},
                entities=[],
                success=False,
                error_message=str(e)
            )
    
    def _query_domain(self, entity: Entity) -> DataSourceResult:
        """Query DNS records for a domain"""
        dns_data = {'domain': entity.value}
        entities = []
        
        # Query different record types
        record_types = ['A', 'AAAA', 'CNAME', 'MX', 'NS', 'TXT', 'SOA']
        
        for record_type in record_types:
            try:
                answers = self.resolver.resolve(entity.value, record_type)
                records = []
                
                for answer in answers:
                    record_value = str(answer)
                    records.append(record_value)
                    
                    # Create entities based on record type
                    if record_type in ['A', 'AAAA']:
                        # IP address records
                        ip_entity = IPAddressEntity(
                            type=EntityType.IP_ADDRESS.value,
                            value=record_value,
                            confidence=0.95,
                            metadata={'source': 'dns', 'record_type': record_type},
                            source=self.name
                        )
                        entities.append(ip_entity)
                        
                    elif record_type in ['CNAME', 'NS']:
                        # Domain records
                        # Remove trailing dot if present
                        domain_value = record_value.rstrip('.')
                        domain_entity = DomainEntity(
                            type=EntityType.DOMAIN.value,
                            value=domain_value,
                            confidence=0.95,
                            metadata={'source': 'dns', 'record_type': record_type},
                            source=self.name
                        )
                        entities.append(domain_entity)
                        
                    elif record_type == 'MX':
                        # Mail exchange records
                        # MX records have priority and domain
                        parts = record_value.split()
                        if len(parts) >= 2:
                            mx_domain = parts[1].rstrip('.')
                            domain_entity = DomainEntity(
                                type=EntityType.DOMAIN.value,
                                value=mx_domain,
                                confidence=0.95,
                                metadata={
                                    'source': 'dns', 
                                    'record_type': record_type,
                                    'mx_priority': parts[0]
                                },
                                source=self.name
                            )
                            entities.append(domain_entity)
                
                dns_data[record_type.lower()] = records
                
            except dns.resolver.NXDOMAIN:
                dns_data[record_type.lower()] = []
            except dns.resolver.NoAnswer:
                dns_data[record_type.lower()] = []
            except Exception as e:
                self.logger.debug(f"Failed to resolve {record_type} for {entity.value}: {e}")
                dns_data[record_type.lower()] = []
        
        return DataSourceResult(
            source=self.name,
            data=dns_data,
            entities=entities,
            success=True
        )
    
    def _query_ip(self, entity: Entity) -> DataSourceResult:
        """Query reverse DNS for an IP address"""
        dns_data = {'ip_address': entity.value}
        entities = []
        
        try:
            # Reverse DNS lookup
            reverse_name = dns.reversename.from_address(entity.value)
            answers = self.resolver.resolve(reverse_name, 'PTR')
            
            hostnames = []
            for answer in answers:
                hostname = str(answer).rstrip('.')
                hostnames.append(hostname)
                
                # Create domain entity
                domain_entity = DomainEntity(
                    type=EntityType.DOMAIN.value,
                    value=hostname,
                    confidence=0.95,
                    metadata={'source': 'dns', 'record_type': 'PTR'},
                    source=self.name
                )
                entities.append(domain_entity)
            
            dns_data['ptr'] = hostnames
            
        except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer):
            dns_data['ptr'] = []
        except Exception as e:
            self.logger.debug(f"Failed reverse DNS lookup for {entity.value}: {e}")
            dns_data['ptr'] = []
        
        return DataSourceResult(
            source=self.name,
            data=dns_data,
            entities=entities,
            success=True
        )
