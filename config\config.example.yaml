# Maltego Configuration File
# Copy this file to config.yaml and customize as needed

app:
  name: "Maltego People Connection Analyzer"
  version: "1.0.0"
  host: "127.0.0.1"
  port: 5000
  debug: false

# Logging configuration
logging:
  level: "INFO"
  file: "logs/maltego.log"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Transform settings
transforms:
  max_depth: 3
  timeout: 30
  parallel_execution: true
  max_workers: 4
  cache_enabled: true
  cache_ttl: 3600  # 1 hour

# Data source configurations
datasources:
  whois:
    enabled: true
    timeout: 10
    rate_limit: 60  # requests per minute

  dns:
    enabled: true
    timeout: 5
    servers:
      - "*******"
      - "*******"

  # Social media APIs (require API keys)
  twitter:
    enabled: false
    api_key: "${TWITTER_API_KEY}"
    api_secret: "${TWITTER_API_SECRET}"
    access_token: "${TWITTER_ACCESS_TOKEN}"
    access_token_secret: "${TWITTER_ACCESS_TOKEN_SECRET}"

  linkedin:
    enabled: false
    client_id: "${LINKEDIN_CLIENT_ID}"
    client_secret: "${LINKEDIN_CLIENT_SECRET}"

  # Search engines
  google:
    enabled: false
    api_key: "${GOOGLE_API_KEY}"
    search_engine_id: "${GOOGLE_SEARCH_ENGINE_ID}"

  bing:
    enabled: false
    api_key: "${BING_API_KEY}"

  # IP Geolocation services
  ipapi:
    enabled: true
    api_key: "${IPAPI_KEY}"  # Optional, free tier available

  # Threat intelligence
  virustotal:
    enabled: false
    api_key: "${VIRUSTOTAL_API_KEY}"

  # Blockchain analysis
  blockchain_info:
    enabled: true
    # No API key required for basic queries

# Caching configuration
cache:
  enabled: true
  directory: "cache"
  default_ttl: 3600
  max_size_mb: 100

# Rate limiting
rate_limiting:
  enabled: true
  requests_per_minute: 60
  burst_limit: 10

# Security settings
security:
  session_secret_key: "${SESSION_SECRET_KEY:-your-secret-key-here}"
  max_analysis_depth: 5
  max_entities_per_analysis: 1000
  allowed_entity_types:
    - "email"
    - "domain"
    - "ip_address"
    - "phone_number"
    - "person"
    - "organization"
    - "social_media"
    - "url"
    - "bitcoin_address"
    - "location"

# Visualization settings
visualization:
  layout: "force"  # force, circular, hierarchical, grid
  max_nodes: 500
  node_size_range: [10, 30]
  edge_width_range: [1, 5]

# Export settings
export:
  formats: ["json", "graphml", "csv"]
  max_file_size_mb: 50

# Directory structure
directories:
  logs: "logs"
  cache: "cache"
  exports: "exports"
  temp: "temp"
