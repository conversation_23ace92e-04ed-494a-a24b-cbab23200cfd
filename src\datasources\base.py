"""
Base classes for data source integrations
"""

import logging
import requests
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from ..entities.base import Entity


@dataclass
class DataSourceResult:
    """Result from a data source query"""
    source: str
    data: Dict[str, Any]
    entities: List[Entity]
    success: bool
    error_message: Optional[str] = None
    response_time: float = 0.0


class BaseDataSource(ABC):
    """Base class for all data sources"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Maltego-Analyzer/1.0'
        })
        
    @property
    @abstractmethod
    def name(self) -> str:
        """Name of the data source"""
        pass
    
    @property
    @abstractmethod
    def supported_entity_types(self) -> List[str]:
        """List of entity types this data source supports"""
        pass
    
    @abstractmethod
    def query(self, entity: Entity) -> DataSourceResult:
        """Query the data source for information about an entity"""
        pass
    
    def is_enabled(self) -> bool:
        """Check if this data source is enabled"""
        return self.config.get('enabled', False)
    
    def get_api_key(self, key_name: str = 'api_key') -> Optional[str]:
        """Get API key from configuration"""
        return self.config.get(key_name)
    
    def make_request(self, url: str, method: str = 'GET', **kwargs) -> requests.Response:
        """Make HTTP request with error handling"""
        start_time = time.time()
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            
            response_time = time.time() - start_time
            self.logger.debug(f"Request to {url} completed in {response_time:.2f}s")
            
            return response
            
        except requests.exceptions.RequestException as e:
            response_time = time.time() - start_time
            self.logger.error(f"Request to {url} failed after {response_time:.2f}s: {e}")
            raise
    
    def supports_entity_type(self, entity_type: str) -> bool:
        """Check if this data source supports the given entity type"""
        return entity_type in self.supported_entity_types
    
    def close(self):
        """Close the data source and cleanup resources"""
        self.session.close()


class DataSourceManager:
    """Manages multiple data sources"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.sources: Dict[str, BaseDataSource] = {}
        
    def register_source(self, source: BaseDataSource):
        """Register a data source"""
        if source.is_enabled():
            self.sources[source.name] = source
            self.logger.info(f"Registered data source: {source.name}")
        else:
            self.logger.info(f"Data source {source.name} is disabled")
    
    def get_sources_for_entity(self, entity_type: str) -> List[BaseDataSource]:
        """Get all enabled data sources that support the given entity type"""
        return [
            source for source in self.sources.values()
            if source.supports_entity_type(entity_type)
        ]
    
    def query_all_sources(self, entity: Entity) -> List[DataSourceResult]:
        """Query all applicable data sources for an entity"""
        applicable_sources = self.get_sources_for_entity(entity.type)
        results = []
        
        for source in applicable_sources:
            try:
                self.logger.info(f"Querying {source.name} for {entity.type}: {entity.value}")
                result = source.query(entity)
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"Error querying {source.name}: {e}")
                results.append(DataSourceResult(
                    source=source.name,
                    data={},
                    entities=[],
                    success=False,
                    error_message=str(e)
                ))
        
        return results
    
    def get_available_sources(self) -> List[str]:
        """Get list of available data source names"""
        return list(self.sources.keys())
    
    def close_all(self):
        """Close all data sources"""
        for source in self.sources.values():
            try:
                source.close()
            except Exception as e:
                self.logger.error(f"Error closing {source.name}: {e}")
        
        self.sources.clear()
