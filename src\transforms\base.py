"""
Base transform classes for Maltego
"""

import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional

from ..entities.base import Entity
from ..datasources.base import DataSourceManager


class BaseTransform(ABC):
    """Base class for all transforms"""
    
    def __init__(self, config: Dict[str, Any], datasource_manager: DataSourceManager):
        self.config = config
        self.datasource_manager = datasource_manager
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
    @property
    @abstractmethod
    def name(self) -> str:
        """Name of the transform"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Description of what the transform does"""
        pass
    
    @property
    @abstractmethod
    def input_types(self) -> List[str]:
        """List of entity types this transform accepts as input"""
        pass
    
    @property
    @abstractmethod
    def output_types(self) -> List[str]:
        """List of entity types this transform can produce"""
        pass
    
    @abstractmethod
    def execute(self, entity: Entity) -> List[Entity]:
        """Execute the transform on an entity and return resulting entities"""
        pass
    
    def is_enabled(self) -> bool:
        """Check if this transform is enabled"""
        enabled_transforms = self.config.get('enabled', [])
        return self.name in enabled_transforms or len(enabled_transforms) == 0
    
    def get_max_results(self) -> int:
        """Get maximum number of results to return"""
        return self.config.get('max_results', 50)
    
    def validate_input(self, entity: Entity) -> bool:
        """Validate that the input entity is supported"""
        return entity.type in self.input_types
    
    def filter_results(self, entities: List[Entity]) -> List[Entity]:
        """Filter and limit results based on configuration"""
        max_results = self.get_max_results()
        
        # Remove duplicates while preserving order
        seen = set()
        unique_entities = []
        for entity in entities:
            entity_key = (entity.type, entity.value)
            if entity_key not in seen:
                seen.add(entity_key)
                unique_entities.append(entity)
        
        # Limit results
        return unique_entities[:max_results]


class TransformRegistry:
    """Registry for managing transforms"""
    
    def __init__(self):
        self.transforms: Dict[str, BaseTransform] = {}
        self.logger = logging.getLogger(__name__)
    
    def register(self, transform: BaseTransform):
        """Register a transform"""
        if transform.is_enabled():
            self.transforms[transform.name] = transform
            self.logger.info(f"Registered transform: {transform.name}")
        else:
            self.logger.info(f"Transform {transform.name} is disabled")
    
    def get_transform(self, name: str) -> Optional[BaseTransform]:
        """Get a transform by name"""
        return self.transforms.get(name)
    
    def get_transforms_for_entity_type(self, entity_type: str) -> List[BaseTransform]:
        """Get all transforms that accept the given entity type"""
        return [
            transform for transform in self.transforms.values()
            if entity_type in transform.input_types
        ]
    
    def get_all_transforms(self) -> List[BaseTransform]:
        """Get all registered transforms"""
        return list(self.transforms.values())
    
    def get_transform_names(self) -> List[str]:
        """Get names of all registered transforms"""
        return list(self.transforms.keys())
